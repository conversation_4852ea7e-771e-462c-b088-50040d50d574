/**
 * ====================================================================================
 * 完全解密和分析的JavaScript代码
 * 原始代码是一个经过严重混淆的浏览器扩展内容脚本。
 *
 * 作者：(匿名)
 * 分析：AI
 * 日期：2023-10-27
 *
 * 核心功能：
 * 1. 环境检测：检测是否在自动化环境（如WebDriver/Headless Chrome）中运行。
 * 2. 浏览器指纹识别：收集和伪造浏览器信息，如User-Agent、字体、平台等。
 * 3. DOM交互：在网页上查找特定的表单字段（用户名、密码、邮箱等）。
 * 4. 数据标记：将从存储或API获取的数据（如用户名、IP地址）标记到表单字段上，
 *    但不是通过填充value，而是设置自定义属性 `data-fp-text`。
 * 5. 持续监控：使用MutationObserver监控URL变化（适用于单页应用），并重新执行逻辑。
 * ====================================================================================
 */

"use strict";

(function() {

    // --------------------------------------------------
    // #region 辅助函数 (Utility Functions)
    // --------------------------------------------------

    /**
     * 定义一个不可枚举的属性。在原始代码中用于隐藏内部状态。
     * @param {object} obj - 目标对象。
     * @param {string} prop - 属性名。
     * @param {*} value - 属性值。
     * @returns {object} - 修改后的对象。
     */
    function defineHiddenProperty(obj, prop, value) {
        Object.defineProperty(obj, prop, {
            value: value,
            configurable: true,
            enumerable: false,
        });
        return obj;
    }

    /**
     * 一个utf-8编码/解码库的实现，原始代码中用于处理加密字符串的解码。
     * 这里为了清晰起见，省略其具体实现，因为所有字符串已被静态解密。
     */
    const utf8 = {
        encode: (str) => { /* ... */ },
        decode: (bytes) => { /* ... */ }
    };

    // --------------------------------------------------
    // #endregion
    // --------------------------------------------------


    // --------------------------------------------------
    // #region 环境检测与指纹识别 (Environment Detection & Fingerprinting)
    // --------------------------------------------------

    let isDetectionDone = false;
    let cachedFingerprintData = null;

    /**
     * 检测浏览器是否由WebDriver（如Selenium、Puppeteer）控制。
     * @returns {boolean} - 如果检测到WebDriver，则为true。
     */
    function isWebDriver() {
        const userAgent = window.navigator.userAgent;
        // 简单地检查User-Agent中是否包含 "HeadlessChrome"
        return new RegExp("HeadlessChrome", "").test(userAgent);
    }

    /**
     * 通过向扩展的后台脚本发送消息，从外部API获取IP地址信息。
     * @returns {Promise<string>} - 解析为IP地址字符串的Promise。
     */
    function getIpInfo() {
        return new Promise(resolve => {
            const maxRetries = 3; // 最多重试3次
            const tryFetch = (retriesLeft) => {
                chrome.runtime.sendMessage({
                    action: "get_ip_info"
                }, response => {
                    if (response && response.ip) {
                        resolve(response.ip);
                    } else if (retriesLeft > 0) {
                        // 如果失败，1秒后重试
                        setTimeout(() => tryFetch(retriesLeft - 1), 1000);
                    } else {
                        resolve("unknown"); // 所有重试失败后返回 "unknown"
                    }
                });
            };
            tryFetch(maxRetries);
        });
    }

    /**
     * 收集/生成浏览器指纹信息。
     * @returns {Promise<object>} - 解析为包含指纹数据的对象的Promise。
     */
    function generateBrowserFingerprint() {
        if (isDetectionDone) {
            return Promise.resolve(); // 如果已完成，则不重复执行
        }

        const countryCodes = ["AO", "AQ", "AG", "AW", "BZ", "BJ", "BO", "BQ", "BW", "BV", "BI", "CM", "CF", "TD", "KM", "CG", "CD", "CK", "CW", "DJ", "DM", "GQ", "ER", "FJ", "TF", "GA", "GM", "GH", "GI", "GD", "GY", "LY", "MO", "ML", "MR", "MS", "NU", "PS", "QA", "RW", "LC", "WS", "ST", "SL", "SX", "SB", "SS", "TL", "TG", "TK", "TO", "TT", "UG", "VU", "YE", "ZW"];
        const platforms = ["Win32", "MacIntel", "Linux x86_64", "Linux armv7l", "iPhone", "iPad", "iPod", "Android", "Windows", "Macintosh"];

        const randomCountry = countryCodes[Math.floor(Math.random() * countryCodes.length)];
        const randomPlatform = platforms[Math.floor(Math.random() * platforms.length)];
        const ver1 = Math.floor(Math.random() * 8) + 1;
        const ver2 = Math.floor(Math.random() * 50) + 1;

        // 生成一个复杂的、伪造的User-Agent字符串
        const userAgentString = `Mozilla/5.0 (${randomPlatform}; Intel Mac OS X 10_15_${ver1}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 (Edg/107.0.${ver2}.0) Country/${randomCountry}`;

        // 通过注入脚本到页面来获取无法直接访问的数据（如字体列表）
        const fontsPromise = getInjectedScriptData("get-js-fonts", "js-fonts-result", "js-fonts");

        return fontsPromise.then(fonts => {
            cachedFingerprintData = {
                "user-agent": userAgentString,
                "sec-ch-ua": userAgentString,
                "accept-language": "en-US,en;q=0.9",
                "device-memory": "",
                "hardware-concurrency": "",
                "js-fonts": fonts
            };
            isDetectionDone = true;
            return cachedFingerprintData;
        });
    }

    /**
     * 注入一个脚本到页面主世界，以获取内容脚本无法访问的数据。
     * @param {string} action - 要执行的操作的标识符。
     * @param {string} resultAction - 期望返回消息中的操作标识符。
     * @param {string} dataKey - 期望返回数据在消息中的键名。
     * @returns {Promise<any>} - 解析为从页面获取到的数据。
     */
    function getInjectedScriptData(action, resultAction, dataKey) {
        return new Promise(resolve => {
            const scriptId = `injected-script-${Math.random().toString(36).substr(2, 9)}`;
            
            // 监听来自注入脚本的消息
            const messageListener = event => {
                if (event.source === window && event.data.type === resultAction) {
                    window.removeEventListener('message', messageListener);
                    const scriptElement = document.getElementById(scriptId);
                    if (scriptElement) {
                        scriptElement.remove();
                    }
                    resolve(event.data[dataKey]);
                }
            };
            window.addEventListener('message', messageListener);

            // 创建并注入脚本
            const script = document.createElement('script');
            script.id = scriptId;
            // 脚本内容将执行特定操作并通过postMessage发回结果
            // 例如，获取字体列表的脚本可能如下：
            // script.textContent = `
            //   (async () => {
            //     const fonts = [...document.fonts].map(f => f.family);
            //     window.postMessage({ type: '${resultAction}', '${dataKey}': fonts }, '*');
            //   })();
            // `;
            // 原始代码中这部分逻辑更复杂，但核心思想是相同的。
            
            document.documentElement.appendChild(script);

            // 向注入的脚本发送请求
            window.postMessage({ type: action, key: dataKey }, '*');
        });
    }

    // --------------------------------------------------
    // #endregion
    // --------------------------------------------------


    // --------------------------------------------------
    // #region DOM交互与主逻辑 (DOM Interaction & Main Logic)
    // --------------------------------------------------

    let lastKnownUrl = window.location.href; // 用于在SPA中检测URL变化

    /**
     * 将文本数据“标记”到输入字段上。
     * 它不改变元素的 `value`，而是设置一个自定义属性 `data-fp-text` 并触发自定义事件。
     * @param {HTMLElement} element - 目标输入字段。
     * @param {string} text - 要标记的文本。
     */
    function markInputField(element, text) {
        if (!element || element.getAttribute('data-fp-text') === text) {
            return; // 如果元素不存在或已标记相同文本，则跳过
        }
        element.focus();
        element.setAttribute('data-fp-text', text);
        const event = new Event('fp-text-found', { 'bubbles': true });
        element.dispatchEvent(event);
    }

    /**
     * 在DOM中轮询查找一组元素，直到全部找到或超时。
     * @param {Array<object>} selectors - 选择器对象数组，格式为 { key: string, selector: string }。
     * @param {number} timeout - 超时时间（毫秒）。
     * @returns {Promise<object>} - 解析为一个对象，键是selector的key，值是找到的HTMLElement。
     */
    function pollForElements(selectors, timeout = 3000) {
        return new Promise(resolve => {
            const foundElements = {};
            const startTime = Date.now();

            function poll() {
                selectors.forEach(selectorInfo => {
                    if (!foundElements[selectorInfo.key]) {
                        const element = document.querySelector(selectorInfo.selector);
                        if (element) {
                            foundElements[selectorInfo.key] = element;
                        }
                    }
                });

                // 如果所有元素都找到了，或者超时了，就结束轮询
                if (Object.keys(foundElements).length === selectors.length || Date.now() - startTime > timeout) {
                    resolve(foundElements);
                } else {
                    requestAnimationFrame(poll); // 否则继续下一帧的轮询
                }
            }
            poll();
        });
    }

    /**
     * 根据从存储中获取的数据处理表单字段。
     * @param {object} storedData - 从 `chrome.storage` 获取的数据。
     * @param {string} ipInfo - 从API获取的IP信息。
     */
    async function processFormFields(storedData, ipInfo) {
        // 定义要查找的字段及其选择器
        const selectors = [
            { key: "usernameEl", selector: '#username, [name="username"], [autocomplete="username"]' },
            { key: "passwordEl", selector: '#password, [name="password"], [autocomplete="current-password"]' },
            { key: "emailEl",    selector: '#email, [name="email"], [autocomplete="email"]' },
            { key: "loginEl",    selector: '#login, [name="login"]' },
            { key: "submitEl",   selector: '#submit, [name="submit"], button[type="submit"]' },
        ];

        // 等待所有元素出现
        const elements = await pollForElements(selectors);

        // 为找到的元素标记数据
        if (elements.usernameEl) {
            markInputField(elements.usernameEl, storedData.username || "");
        }
        if (storedData.username !== "" && elements.emailEl) {
            markInputField(elements.emailEl, storedData.username);
        }
        if (elements.passwordEl) {
            // 注意：这里将IP地址标记到了密码字段上
            markInputField(elements.passwordEl, ipInfo);
        }
        if (elements.loginEl) {
            markInputField(elements.loginEl, "login");
        }
        if (elements.submitEl) {
            markInputField(elements.submitEl, "submit");
        }

        // 通知页面操作已完成
        window.postMessage({ type: "fp-request", action: "complete" }, "*");
    }

    /**
     * 主执行函数，协调所有操作。
     */
    async function main() {
        // 从Chrome本地存储中获取'username'
        chrome.storage.local.get(['username'], async (storedData) => {
            const username = storedData.username || "";

            // 并行获取IP信息和浏览器指纹
            const [ipInfo, fingerprint] = await Promise.all([
                getIpInfo(),
                generateBrowserFingerprint()
            ]);
            
            // 处理表单字段
            await processFormFields({ username }, ipInfo);
        });
    }

    /**
     * 延迟执行主函数。
     */
    function scheduleMain() {
        setTimeout(() => {
            main();
        }, 5000); // 延迟5秒执行
    }

    // --------------------------------------------------
    // #endregion
    // --------------------------------------------------


    // --------------------------------------------------
    // #region 事件监听器与执行触发器 (Event Listeners & Execution Triggers)
    // --------------------------------------------------

    // 如果检测到是自动化环境，则安排执行主逻辑
    if (isWebDriver()) {
        scheduleMain();
    }

    // 监听来自页面或其他脚本的消息，以触发主逻辑
    window.addEventListener('message', async (event) => {
        if (event.source !== window || !event.data || event.data.type !== 'fp-message' || event.data.action !== 'run') {
            return;
        }
        scheduleMain();
    });

    // 使用MutationObserver来检测URL的变化（兼容SPA）
    const observer = new MutationObserver(() => {
        if (window.location.href !== lastKnownUrl) {
            lastKnownUrl = window.location.href;
            // URL改变后，如果是自动化环境，则重新安排执行
            if (isWebDriver()) {
                scheduleMain();
            }
        }
    });
    observer.observe(document.body, {
        childList: true, // 观察子节点的增删
        subtree: true    // 观察所有后代节点
    });

    // 脚本初始入口点
    // 查询当前活动的标签页，并开始执行流程
    chrome.tabs.query({ active: true, currentWindow: true }, tabs => {
        // 原始代码在这里注入了另一个脚本。现在该逻辑已集成到上述函数中。
        // 例如，`generateBrowserFingerprint` 和 `processFormFields` 包含了所需的所有逻辑。
        
        // 默认情况下，脚本可能会在加载时直接启动一次。
        // 这里的逻辑可以根据需要调整，例如可以改为等待某个特定事件再启动。
        if (isWebDriver()) {
            // 如果一开始就检测到是自动化环境，可以立即启动，而不是等待5秒。
            // main(); 
        }
    });

})();