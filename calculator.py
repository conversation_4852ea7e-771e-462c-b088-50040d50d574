"""
简单的Python计算器
支持基本的四则运算：加法、减法、乘法、除法
"""

def add(a, b):
    """
    加法运算
    
    Args:
        a (float): 第一个数
        b (float): 第二个数
    
    Returns:
        float: 两数之和
    """
    return a + b

def subtract(a, b):
    """
    减法运算
    
    Args:
        a (float): 被减数
        b (float): 减数
    
    Returns:
        float: 两数之差
    """
    return a - b

def multiply(a, b):
    """
    乘法运算
    
    Args:
        a (float): 第一个数
        b (float): 第二个数
    
    Returns:
        float: 两数之积
    """
    return a * b

def divide(a, b):
    """
    除法运算
    
    Args:
        a (float): 被除数
        b (float): 除数
    
    Returns:
        float: 两数之商
    
    Raises:
        ValueError: 当除数为0时抛出异常
    """
    if b == 0:
        raise ValueError("错误：除数不能为零")
    return a / b

def validate_number(value):
    """
    验证输入是否为有效数字
    
    Args:
        value (str): 待验证的字符串
    
    Returns:
        float: 转换后的数字
    
    Raises:
        ValueError: 当输入不是有效数字时抛出异常
    """
    try:
        return float(value)
    except ValueError:
        raise ValueError(f"错误：'{value}' 不是有效的数字")

def get_operation_function(operator):
    """
    根据操作符获取对应的运算函数
    
    Args:
        operator (str): 运算符 (+, -, *, /)
    
    Returns:
        function: 对应的运算函数
    
    Raises:
        ValueError: 当操作符不支持时抛出异常
    """
    operations = {
        '+': add,
        '-': subtract,
        '*': multiply,
        '/': divide
    }
    
    if operator not in operations:
        raise ValueError(f"错误：不支持的运算符 '{operator}'")
    
    return operations[operator]
