#!/usr/bin/env python3
"""
简单的文件整理和分析脚本
功能：
1. 分析项目文件结构
2. 统计不同类型文件的数量和大小
3. 查找重复文件
4. 生成项目报告
"""

import os
import hashlib
import json
from collections import defaultdict
from datetime import datetime
import argparse


class FileOrganizer:
    def __init__(self, root_path="."):
        self.root_path = os.path.abspath(root_path)
        self.file_stats = defaultdict(lambda: {"count": 0, "total_size": 0})
        self.duplicate_files = defaultdict(list)
        self.all_files = []
        
    def get_file_hash(self, filepath):
        """计算文件的MD5哈希值"""
        try:
            with open(filepath, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except (IOError, OSError):
            return None
    
    def analyze_files(self):
        """分析所有文件"""
        print(f"正在分析目录: {self.root_path}")
        
        for root, dirs, files in os.walk(self.root_path):
            # 跳过隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.startswith('.'):
                    continue
                    
                filepath = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(filepath)
                    file_ext = os.path.splitext(file)[1].lower()
                    if not file_ext:
                        file_ext = "无扩展名"
                    
                    # 统计文件类型
                    self.file_stats[file_ext]["count"] += 1
                    self.file_stats[file_ext]["total_size"] += file_size
                    
                    # 记录文件信息
                    file_info = {
                        "path": os.path.relpath(filepath, self.root_path),
                        "size": file_size,
                        "ext": file_ext,
                        "hash": self.get_file_hash(filepath)
                    }
                    self.all_files.append(file_info)
                    
                    # 检查重复文件
                    if file_info["hash"]:
                        self.duplicate_files[file_info["hash"]].append(file_info)
                        
                except (OSError, IOError) as e:
                    print(f"无法访问文件 {filepath}: {e}")
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "="*50)
        print("📊 文件统计报告")
        print("="*50)
        
        total_files = sum(stats["count"] for stats in self.file_stats.values())
        total_size = sum(stats["total_size"] for stats in self.file_stats.values())
        
        print(f"总文件数: {total_files}")
        print(f"总大小: {self.format_size(total_size)}")
        print(f"分析目录: {self.root_path}")
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📁 文件类型分布:")
        print("-" * 40)
        
        # 按文件数量排序
        sorted_stats = sorted(self.file_stats.items(), 
                            key=lambda x: x[1]["count"], reverse=True)
        
        for ext, stats in sorted_stats:
            percentage = (stats["count"] / total_files) * 100
            print(f"{ext:15} | {stats['count']:4d} 个 | "
                  f"{self.format_size(stats['total_size']):>8} | {percentage:5.1f}%")
    
    def find_duplicates(self):
        """查找重复文件"""
        duplicates = {hash_val: files for hash_val, files in self.duplicate_files.items() 
                     if len(files) > 1}
        
        if duplicates:
            print("\n🔍 发现重复文件:")
            print("-" * 40)
            for hash_val, files in duplicates.items():
                print(f"\n重复组 (哈希: {hash_val[:8]}...):")
                for file_info in files:
                    print(f"  📄 {file_info['path']} ({self.format_size(file_info['size'])})")
        else:
            print("\n✅ 未发现重复文件")
    
    def generate_report(self, output_file="project_report.json"):
        """生成详细的JSON报告"""
        report = {
            "analysis_time": datetime.now().isoformat(),
            "root_path": self.root_path,
            "summary": {
                "total_files": len(self.all_files),
                "total_size": sum(f["size"] for f in self.all_files),
                "file_types": dict(self.file_stats)
            },
            "files": self.all_files,
            "duplicates": {hash_val: files for hash_val, files in self.duplicate_files.items() 
                          if len(files) > 1}
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 详细报告已保存到: {output_file}")
    
    def clean_empty_dirs(self):
        """清理空目录"""
        empty_dirs = []
        for root, dirs, files in os.walk(self.root_path, topdown=False):
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):
                        empty_dirs.append(os.path.relpath(dir_path, self.root_path))
                except OSError:
                    continue
        
        if empty_dirs:
            print(f"\n📂 发现 {len(empty_dirs)} 个空目录:")
            for empty_dir in empty_dirs:
                print(f"  📁 {empty_dir}")
            
            if input("\n是否删除这些空目录? (y/N): ").lower() == 'y':
                for empty_dir in empty_dirs:
                    try:
                        os.rmdir(os.path.join(self.root_path, empty_dir))
                        print(f"✅ 已删除: {empty_dir}")
                    except OSError as e:
                        print(f"❌ 删除失败 {empty_dir}: {e}")
        else:
            print("\n✅ 未发现空目录")


def main():
    parser = argparse.ArgumentParser(description="文件整理和分析工具")
    parser.add_argument("path", nargs="?", default=".", help="要分析的目录路径")
    parser.add_argument("--report", "-r", help="生成JSON报告文件名")
    parser.add_argument("--clean", "-c", action="store_true", help="清理空目录")
    
    args = parser.parse_args()
    
    organizer = FileOrganizer(args.path)
    
    # 分析文件
    organizer.analyze_files()
    
    # 显示统计信息
    organizer.print_statistics()
    
    # 查找重复文件
    organizer.find_duplicates()
    
    # 生成报告
    if args.report:
        organizer.generate_report(args.report)
    
    # 清理空目录
    if args.clean:
        organizer.clean_empty_dirs()


if __name__ == "__main__":
    main()
