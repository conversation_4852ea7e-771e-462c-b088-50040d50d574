<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .controls {
            display: flex;
            gap: 10px;
        }
        
        button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .start-btn {
            background: #4CAF50;
            color: white;
        }
        
        .start-btn:hover {
            background: #45a049;
        }
        
        .stop-btn {
            background: #f44336;
            color: white;
        }
        
        .stop-btn:hover {
            background: #da190b;
        }
        
        .start-btn:disabled,
        .stop-btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 6px;
            margin-top: 15px;
            max-height: 100px;
            overflow-y: auto;
            font-size: 11px;
            font-family: monospace;
        }
        
        .indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .indicator.active {
            background: #4CAF50;
            animation: pulse 1s infinite;
        }
        
        .indicator.inactive {
            background: #f44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Auto Checkouter</div>
        <div class="subtitle">Automated Payment Processor</div>
    </div>
    
    <div class="status">
        <div class="status-item">
            <span>Status:</span>
            <span id="status">
                <span class="indicator inactive"></span>
                Inactive
            </span>
        </div>
        <div class="status-item">
            <span>Attempts:</span>
            <span id="attempts">0/5</span>
        </div>
        <div class="status-item">
            <span>Page Type:</span>
            <span id="pageType">Unknown</span>
        </div>
    </div>
    
    <div class="controls">
        <button id="startBtn" class="start-btn">Start</button>
        <button id="stopBtn" class="stop-btn" disabled>Stop</button>
    </div>
    
    <div class="log" id="log">
        <div>Ready to start auto checkout...</div>
    </div>
    
    <script src="popup_deobfuscated.js"></script>
</body>
</html>
