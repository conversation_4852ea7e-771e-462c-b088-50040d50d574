// Popup script for Auto Checkouter (Deobfuscated)
"use strict";

class PopupController {
    constructor() {
        this.statusElement = document.getElementById('status');
        this.attemptsElement = document.getElementById('attempts');
        this.pageTypeElement = document.getElementById('pageType');
        this.logElement = document.getElementById('log');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        
        this.init();
    }
    
    init() {
        // Bind event listeners
        this.startBtn.addEventListener('click', () => this.startAutoCheckout());
        this.stopBtn.addEventListener('click', () => this.stopAutoCheckout());
        
        // Update status on load
        this.updateStatus();
        this.checkPageType();
        
        // Update status every 2 seconds
        setInterval(() => this.updateStatus(), 2000);
    }
    
    async startAutoCheckout() {
        try {
            const response = await this.sendMessage({action: 'startAutoCheckout'});
            if (response && response.status === 'started') {
                this.log('Auto checkout started');
                this.updateButtonStates(true);
            }
        } catch (error) {
            this.log('Error starting auto checkout: ' + error.message);
        }
    }
    
    async stopAutoCheckout() {
        try {
            const response = await this.sendMessage({action: 'stopAutoCheckout'});
            if (response && response.status === 'stopped') {
                this.log('Auto checkout stopped');
                this.updateButtonStates(false);
            }
        } catch (error) {
            this.log('Error stopping auto checkout: ' + error.message);
        }
    }
    
    async updateStatus() {
        try {
            const response = await this.sendMessage({action: 'getStatus'});
            if (response) {
                this.updateStatusDisplay(response);
            }
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }
    
    updateStatusDisplay(status) {
        // Update status indicator
        const indicator = this.statusElement.querySelector('.indicator');
        if (status.isProcessing) {
            indicator.className = 'indicator active';
            this.statusElement.innerHTML = '<span class="indicator active"></span>Active';
            this.updateButtonStates(true);
        } else {
            indicator.className = 'indicator inactive';
            this.statusElement.innerHTML = '<span class="indicator inactive"></span>Inactive';
            this.updateButtonStates(false);
        }
        
        // Update attempts
        this.attemptsElement.textContent = `${status.retryCount || 0}/${status.maxRetries || 5}`;
    }
    
    updateButtonStates(isRunning) {
        this.startBtn.disabled = isRunning;
        this.stopBtn.disabled = !isRunning;
    }
    
    async checkPageType() {
        try {
            const tabs = await this.getCurrentTab();
            if (tabs && tabs[0]) {
                const url = tabs[0].url.toLowerCase();
                let pageType = 'Regular Page';
                
                if (url.includes('checkout')) pageType = 'Checkout Page';
                else if (url.includes('payment')) pageType = 'Payment Page';
                else if (url.includes('stripe')) pageType = 'Stripe Page';
                else if (url.includes('pay')) pageType = 'Payment Page';
                else if (url.includes('billing')) pageType = 'Billing Page';
                
                this.pageTypeElement.textContent = pageType;
                
                if (pageType !== 'Regular Page') {
                    this.log(`Detected: ${pageType}`);
                }
            }
        } catch (error) {
            console.error('Error checking page type:', error);
        }
    }
    
    getCurrentTab() {
        return new Promise((resolve) => {
            chrome.tabs.query({active: true, currentWindow: true}, resolve);
        });
    }
    
    sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }
    
    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        this.logElement.appendChild(logEntry);
        this.logElement.scrollTop = this.logElement.scrollHeight;
        
        // Keep only last 10 log entries
        while (this.logElement.children.length > 10) {
            this.logElement.removeChild(this.logElement.firstChild);
        }
    }
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'paymentSuccess') {
        popupController.log('✓ Payment successful!');
    } else if (request.action === 'paymentFailure') {
        popupController.log('✗ Payment failed after all retries');
    } else if (request.action === 'log') {
        popupController.log(request.message);
    }
});

// Initialize popup controller
const popupController = new PopupController();

// Add some helpful information
document.addEventListener('DOMContentLoaded', () => {
    popupController.log('Popup loaded and ready');
    
    // Show instructions if not on a checkout page
    setTimeout(async () => {
        const tabs = await popupController.getCurrentTab();
        if (tabs && tabs[0]) {
            const url = tabs[0].url.toLowerCase();
            const isCheckoutPage = ['checkout', 'payment', 'stripe', 'pay', 'billing']
                .some(keyword => url.includes(keyword));
            
            if (!isCheckoutPage) {
                popupController.log('Navigate to a checkout page to use auto checkout');
            } else {
                popupController.log('Checkout page detected - ready to start');
            }
        }
    }, 1000);
});
