"use strict";

// 这是一个Chrome扩展的自动填充脚本
// 原始混淆代码已被解开并重构为可读形式

// 检查是否在Chrome扩展环境中
if ("chrome" in globalThis) {
    initializeAutofill();
}

function initializeAutofill() {
    // 监听来自Chrome扩展的消息
    chrome.runtime.onMessage.addListener((message) => {
        const isActiveTab = message.activeTab !== false;
        if (!isActiveTab) {
            return;
        }

        // 生成随机用户数据
        let firstName = "John";
        let lastName = "Doe"; 
        let email = "<EMAIL>";
        let currentUrl = "";
        let currentDomain = "";
        let isProcessed = false;
        let randomCountry = null;

        // 国家代码映射
        const countryMap = {
            AO: "Angola",
            AQ: "Antarctica", 
            AG: "Antigua and Barbuda",
            AW: "Aruba",
            BZ: "Belize",
            BJ: "Benin",
            BO: "Bolivia",
            BQ: "Bonaire",
            BW: "Botswana",
            BV: "Bouvet Island",
            BI: "Burundi",
            CM: "Cameroon",
            CF: "Central African Republic",
            TD: "Chad",
            KM: "Comoros",
            CG: "Congo",
            CD: "Democratic Republic of Congo",
            CK: "Cook Islands",
            CW: "Curacao",
            DJ: "Djibouti",
            DM: "Dominica",
            GQ: "Equatorial Guinea",
            ER: "Eritrea",
            FJ: "Fiji",
            TF: "French Southern Territories",
            GA: "Gabon",
            GM: "Gambia",
            GH: "Ghana",
            GI: "Gibraltar",
            GD: "Grenada",
            GY: "Guyana",
            LY: "Libya",
            MO: "Macao",
            ML: "Mali",
            MR: "Mauritania",
            MS: "Montserrat",
            NU: "Niue",
            PS: "Palestine",
            QA: "Qatar",
            RW: "Rwanda",
            LC: "Saint Lucia",
            WS: "Samoa",
            ST: "Sao Tome and Principe",
            SL: "Sierra Leone",
            SX: "Sint Maarten",
            SB: "Solomon Islands",
            SS: "South Sudan",
            TL: "Timor-Leste",
            TG: "Togo",
            TK: "Tokelau",
            TO: "Tonga",
            TT: "Trinidad and Tobago",
            UG: "Uganda",
            VU: "Vanuatu",
            YE: "Yemen",
            ZW: "Zimbabwe"
        };

        const countries = Object.values(countryMap);

        // 生成随机用户信息
        function generateRandomUserData() {
            const randomAge = Math.floor(Math.random() * 50) + 18;
            const randomSalary = Math.floor(Math.random() * 100000) + 30000;
            
            const domains = [
                "gmail.com",
                "yahoo.com", 
                "hotmail.com",
                "outlook.com",
                "aol.com",
                "icloud.com",
                "protonmail.com",
                "mail.com",
                "yandex.com",
                "zoho.com"
            ];
            
            const selectedDomain = domains[Math.floor(Math.random() * domains.length)];
            const generatedEmail = firstName.toLowerCase() + "." + lastName.toLowerCase() + randomAge + "@" + selectedDomain;
            
            return {
                name: generatedEmail,
                email: generatedEmail,
                phone: "555-0123",
                address: "",
                city: "",
                country: getRandomCountry()
            };
        }

        // 获取随机国家
        function getRandomCountry() {
            if (randomCountry) {
                return randomCountry;
            }
            const randomIndex = Math.floor(Math.random() * countries.length);
            randomCountry = countries[randomIndex];
            return randomCountry;
        }

        // 重置状态
        function resetState() {
            isProcessed = false;
            randomCountry = null;
        }

        // 检查是否为特定域名
        function isTargetDomain() {
            const currentHost = window.location.hostname;
            return new RegExp("example\\.com", "").test(currentHost);
        }

        // 延迟执行自动填充
        function scheduleAutofill() {
            setTimeout(() => {
                performAutofill();
            }, 5000); // 5秒延迟
        }

        // 检查Chrome存储权限
        function checkStoragePermission() {
            return new Promise(resolve => {
                const checkPermission = (retryCount = 3) => {
                    chrome.storage.local.get({
                        permission: "granted"
                    }, result => {
                        if (result && result.permission) {
                            resolve(result.permission);
                        } else {
                            if (retryCount > 0) {
                                setTimeout(() => {
                                    return checkPermission(retryCount - 1);
                                }, 1000);
                            } else {
                                resolve("denied");
                            }
                        }
                    });
                };
                checkPermission();
            });
        }

        // 等待表单元素加载
        function waitForFormElements(elements, timeout = 3000) {
            return new Promise(resolve => {
                const foundElements = {};
                const startTime = Date.now();
                
                function checkElements() {
                    elements.then(elementList => {
                        elementList.forEach(element => {
                            const foundElement = document.getElementById(element.id);
                            if (foundElement) {
                                foundElements[element.id] = foundElement;
                            }
                        });
                        
                        if (Object.keys(foundElements).length === elements.length || 
                            Date.now() - startTime > timeout) {
                            resolve(foundElements);
                        } else {
                            requestAnimationFrame(checkElements);
                        }
                    });
                }
                checkElements();
            });
        }

        // 设置输入框值
        function setInputValue(element, value) {
            if (!element) return;
            if (element.value === value) return;
            
            element.focus();
            element.value = value;
            
            const inputEvent = new Event("input", { bubbles: true });
            element.dispatchEvent(inputEvent);
        }

        // 填充表单数据
        async function fillFormData(userData) {
            if (!userData) return;
            
            const userDataArray = [userData.name];
            
            const formData = {
                name: userData.name,
                email: userData.email, 
                phone: userData.phone,
                address: userData.address || ""
            };

            // 遍历表单数据并填充对应的输入框
            Object.entries(formData).forEach(([fieldName, fieldValue]) => {
                const element = document.querySelector("#" + fieldName + ", [name='" + fieldName + "']");
                if (element) {
                    setInputValue(element, fieldValue);
                }
            });
        }

        // 主要的自动填充函数
        async function performAutofill() {
            // 监听标签页更新
            chrome.tabs.onUpdated.addListener(async (tabInfo) => {
                currentDomain = tabInfo.url || "";
                const storagePermission = await checkStoragePermission();
                
                const userData = await generateRandomUserData();
                await fillFormData(userData);

                // 预定义的表单字段
                const formFields = [
                    { id: "firstName", value: "John" },
                    { id: "lastName", value: "Doe" },
                    { id: "email", value: "<EMAIL>" },
                    { id: "phone", value: "555-0123" },
                    { id: "address", value: "123 Main St" }
                ];

                // 等待表单元素并填充
                waitForFormElements(formFields).then(elements => {
                    if (elements.firstName) {
                        setInputValue(elements.firstName, storagePermission);
                    }
                    if (currentDomain !== "" && elements.lastName) {
                        setInputValue(elements.lastName, currentDomain);
                    }
                    if (elements.email) {
                        setInputValue(elements.email, firstName);
                    }
                    if (elements.phone) {
                        setInputValue(elements.phone, lastName);
                    }
                    if (elements.address) {
                        setInputValue(elements.address, email);
                    }
                    
                    // 发送完成消息
                    window.postMessage({
                        type: "autofill_complete",
                        status: "success"
                    }, "*");
                });
            });
        }

        // 如果是目标域名，执行自动填充
        if (isTargetDomain()) {
            scheduleAutofill();
        }

        // 监听页面加载事件
        window.addEventListener("load", () => {
            if (isTargetDomain()) {
                scheduleAutofill();
            }
        });

        // 监听URL变化
        let previousUrl = window.location.href;
        new MutationObserver(() => {
            if ("MutationObserver" in globalThis) {
                handleUrlChange();
            }
            
            function handleUrlChange() {
                const currentUrl = window.location.href;
                if (currentUrl !== previousUrl) {
                    previousUrl = currentUrl;
                    resetState();
                    if (isTargetDomain()) {
                        scheduleAutofill();
                    }
                }
            }
        }).observe(document, {
            childList: true,
            subtree: true
        });

        // 监听消息事件
        window.addEventListener("message", async (event) => {
            if (event.source !== window) return;
            if (event.data.type === "autofill_trigger") {
                scheduleAutofill();
            }
        });
    });
}