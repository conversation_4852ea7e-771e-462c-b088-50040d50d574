"use strict";

// 完全解混淆的自动填充脚本
// 这是一个Chrome扩展的内容脚本，用于自动填充表单

// 检查是否在Chrome扩展环境中
if ("chrome" in globalThis) {
    initializeAutofill();
}

function initializeAutofill() {
    // 监听来自Chrome扩展的消息
    chrome.runtime.onMessage.addListener((message) => {
        const isActiveTab = message.activeTab !== false;
        if (!isActiveTab) {
            return;
        }

        // 创建空对象用于存储数据
        let formData = Object.create(null);
        let currentArgs = undefined;

        // 主要的表单填充函数
        function fillForm(selector, fillValue, targetElement, options = { bubbles: true }, decoder, cache, eventType, handler) {
            // 如果没有提供缓存函数，创建一个
            if (!cache) {
                cache = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = decoder(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }

            // 如果没有提供解码器，创建一个
            if (!decoder) {
                decoder = function(encodedStr) {
                    const alphabet = "*cNBHQfimDUrSRtEhJqFYAGXVPWM(_CZLx}l6oz?#b$up{`O\";I~Kjg+da0]5v,:43!w>^&%[12=9/8@e<T)y|.k7ns";
                    // 解码逻辑...
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            let currentElement = undefined;
            let handlers = {
                // 获取随机国家
                getRandomCountry: function(decoder, cache) {
                    if (!cache) {
                        cache = function(index) {
                            if (typeof stringCache[index] === "undefined") {
                                return stringCache[index] = decoder(encodedStrings[index]);
                            }
                            return stringCache[index];
                        };
                    }
                    if (!decoder) {
                        decoder = function(encodedStr) {
                            const alphabet = "hNAmDX&oPHvu`Opw|r72/BE>68?+Cjl{QJbsG_3YiZxf$STtyM[e;V90d#FKqzkRI1aWgL5U<=n%\"~]^4}*)@.,(:!c";
                            return decodeBase91String(encodedStr, alphabet);
                        };
                    }

                    if (randomCountry) {
                        return randomCountry;
                    }
                    const randomIndex = Math.floor(Math.random() * countries.length);
                    randomCountry = countries[randomIndex];
                    return randomCountry;
                },

                // 检查URL变化
                checkUrlChange: function(decoder, cache) {
                    if (!cache) {
                        cache = function(index) {
                            if (typeof stringCache[index] === "undefined") {
                                return stringCache[index] = decoder(encodedStrings[index]);
                            }
                            return stringCache[index];
                        };
                    }
                    if (!decoder) {
                        decoder = function(encodedStr) {
                            const alphabet = "Z.GkHdDYTWrXjeBIQKtVlNcg?~aMU_$}3u)o+EvxSi\"y=76]1[8>w94Rhzp5@n&^{s(Am<;/bO%`q#fC,JP0F2:*L|!";
                            return decodeBase91String(encodedStr, alphabet);
                        };
                    }

                    let [currentUrl] = currentArgs;
                    if (isProcessed) {
                        return;
                    }

                    const targetInput = document.getElementById("email");
                    if (targetInput && targetInput.value !== currentUrl) {
                        // 设置输入框值
                        window.postMessage({
                            type: "autofill_data",
                            value: "<EMAIL>"
                        }, "*");
                        
                        targetInput.value = currentUrl;
                        
                        const inputEvent = new Event("input", { bubbles: true });
                        targetInput.dispatchEvent(inputEvent);
                        
                        window.postMessage({
                            type: "autofill_complete", 
                            value: "success"
                        }, "*");
                        
                        isProcessed = true;
                    }
                }
            };

            if (selector === "getRandomCountry") {
                currentArgs = formData[selector] || (formData[selector] = createRandomUserData());
            } else {
                currentElement = handlers[selector]();
            }

            if (targetElement === "return") {
                return { result: currentElement };
            } else {
                return currentElement;
            }
        }

        // 检查消息类型
        const messageType = message.type !== false;
        if (!messageType) {
            return;
        }

        // 初始化变量
        let firstName = "John";
        let lastName = "Doe";
        let email = "<EMAIL>";
        let currentUrl = "";
        let currentDomain = "";
        let isProcessed = false;
        let randomCountry = null;

        // 国家代码映射
        const countryMap = {
            AO: "Angola",
            AQ: "Antarctica",
            AG: "Antigua and Barbuda",
            AW: "Aruba",
            BZ: "Belize",
            BJ: "Benin",
            BO: "Bolivia",
            BQ: "Bonaire",
            BW: "Botswana",
            BV: "Bouvet Island",
            BI: "Burundi",
            CM: "Cameroon",
            CF: "Central African Republic",
            TD: "Chad",
            KM: "Comoros",
            CG: "Congo",
            CD: "Democratic Republic of Congo",
            CK: "Cook Islands",
            CW: "Curacao",
            DJ: "Djibouti",
            DM: "Dominica",
            GQ: "Equatorial Guinea",
            ER: "Eritrea",
            FJ: "Fiji",
            TF: "French Southern Territories",
            GA: "Gabon",
            GM: "Gambia",
            GH: "Ghana",
            GI: "Gibraltar",
            GD: "Grenada",
            GY: "Guyana",
            LY: "Libya",
            MO: "Macao",
            ML: "Mali",
            MR: "Mauritania",
            MS: "Montserrat",
            NU: "Niue",
            PS: "Palestine",
            QA: "Qatar",
            RW: "Rwanda",
            LC: "Saint Lucia",
            WS: "Samoa",
            ST: "Sao Tome and Principe",
            SL: "Sierra Leone",
            SX: "Sint Maarten",
            SB: "Solomon Islands",
            SS: "South Sudan",
            TL: "Timor-Leste",
            TG: "Togo",
            TK: "Tokelau",
            TO: "Tonga",
            TT: "Trinidad and Tobago",
            UG: "Uganda",
            VU: "Vanuatu",
            YE: "Yemen",
            ZW: "Zimbabwe"
        };

        const countries = Object.values(countryMap);

        // 生成随机用户数据
        function createRandomUserData(decoder, cache) {
            if (!cache) {
                cache = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = decoder(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!decoder) {
                decoder = function(encodedStr) {
                    const alphabet = ",gFLsGiVRtAZ4K~=zB0mw2(vr3O)c<+pehjNnx>k]H{&d_I1uC6SU*qY\"E9!}?f7;#`P:.8@|y%Jo[T5M/XQ$b^WDla";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            const randomAge = Math.floor(Math.random() * 50) + 18;
            const randomSalary = Math.floor(Math.random() * 100000) + 30000;
            
            const domains = [
                "gmail.com",
                "yahoo.com",
                "hotmail.com", 
                "outlook.com",
                "aol.com",
                "icloud.com",
                "protonmail.com",
                "mail.com",
                "yandex.com",
                "zoho.com"
            ];
            
            const selectedDomain = domains[Math.floor(Math.random() * domains.length)];
            const generatedEmail = firstName.toLowerCase() + "." + lastName.toLowerCase() + randomAge + "@" + selectedDomain;
            
            return {
                name: generatedEmail,
                email: generatedEmail,
                phone: "555-0123",
                address: "",
                city: "",
                country: fillForm("getRandomCountry", "getRandomCountry", "getRandomCountry").result
            };
        }

        // 重置状态
        function resetState() {
            isProcessed = false;
            randomCountry = null;
        }

        // 获取用户数据的Promise
        function getUserData(decoder, cache, resolver) {
            if (!resolver) {
                resolver = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = cache(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!cache) {
                cache = function(encodedStr) {
                    const alphabet = "R#kCrGzl|_8:\"q&S9i,D)VE}2a4h$usd^B>p5P3U<OL]tm.Ffw;N*~ZK?g`x(T7A[oYvy=6{eM!jI0JW@1X+cHnQb/%";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }
            return Promise.resolve(createRandomUserData());
        }

        // 设置输入框值
        function setInputValue(element, value, decoder, cache) {
            if (!cache) {
                cache = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = decoder(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!decoder) {
                decoder = function(encodedStr) {
                    const alphabet = "1u{(%Tm+5r#fL}W~>ov3g7UAZtS&Jwl)N^2aBxVQFy,\"Yn9]I6@4|PR=[*8C<0DdH:j.Mp$K!eOXk;`sz_?Eqibc/hG";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            if (!element) return;
            if (element.value === value) return;
            
            element.focus();
            element.value = value;
            
            const inputEvent = new Event("input", { bubbles: true });
            element.dispatchEvent(inputEvent);
        }

        // 等待元素加载
        function waitForElements(elements, timeout = 3000) {
            return new Promise(resolve => {
                const foundElements = {};
                const startTime = Date.now();
                
                function checkElements() {
                    elements.then(elementList => {
                        elementList.forEach(element => {
                            const foundElement = document.getElementById(element.id);
                            if (foundElement) {
                                foundElements[element.id] = foundElement;
                            }
                        });
                        
                        if (Object.keys(foundElements).length === elements.length || 
                            Date.now() - startTime > timeout) {
                            resolve(foundElements);
                        } else {
                            requestAnimationFrame(checkElements);
                        }
                    });
                }
                checkElements();
            });
        }

        // 检查域名
        function checkDomain(decoder, cache) {
            if (!cache) {
                cache = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = decoder(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!decoder) {
                decoder = function(encodedStr) {
                    const alphabet = "udRtPXiAkSBr`1]Wo3U}?{!O*Qp%VH+Zvjq5<w6hYJF(~@#\"clNs)gm.f^&xnG7;MT9y,$[z4C0I:D=2/_>LaeKE|b8";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            const currentHost = window.location.hostname;
            return new RegExp("example\\.com", "").test(currentHost);
        }

        // 延迟执行自动填充
        function scheduleAutofill() {
            setTimeout(() => {
                performAutofill();
            }, 5000);
        }

        // 检查Chrome存储��限
        function checkStoragePermission() {
            return new Promise(resolve => {
                const checkPermission = (retryCount = 3) => {
                    chrome.storage.local.get({
                        permission: "granted"
                    }, result => {
                        if (result && result.permission) {
                            resolve(result.permission);
                        } else {
                            if (retryCount > 0) {
                                setTimeout(() => {
                                    return checkPermission(retryCount - 1);
                                }, 1000);
                            } else {
                                resolve("denied");
                            }
                        }
                    });
                };
                checkPermission();
            });
        }

        // 填充表单数据
        async function fillFormData(userData, cache, resolver) {
            if (!resolver) {
                resolver = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = cache(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!cache) {
                cache = function(encodedStr) {
                    const alphabet = ".=1{<5*9@uqTno!F3v}f2k|U~+K[jN;H8Y]JMw/6Vl$rEmt4`>%&iD#0P,eh?c7C^(ZGWxLBsOpbR)gS_\"XIy:AzdaQ";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            if (!userData) return;
            
            const userDataArray = [userData.name];
            
            const formData = {
                name: userData.name,
                email: userData.email,
                phone: userData.phone,
                address: userData.address || ""
            };

            // 遍历表单数据并填充对应的输入框
            Object.entries(formData).forEach(([fieldName, fieldValue]) => {
                const element = document.querySelector("#" + fieldName + ", [name='" + fieldName + "']");
                if (element) {
                    setInputValue(element, fieldValue);
                }
            });
        }

        // 主要的自动填充函数
        async function performAutofill(cache, resolver) {
            if (!resolver) {
                resolver = function(index) {
                    if (typeof stringCache[index] === "undefined") {
                        return stringCache[index] = cache(encodedStrings[index]);
                    }
                    return stringCache[index];
                };
            }
            if (!cache) {
                cache = function(encodedStr) {
                    const alphabet = "iADlJeCbRIckNGVQPMfELZrB5ypn1q7TX[)jK4hHt`a}g~m(_sF#!UOxWdS>Y&08<zw@=vu9^\"${:|;,.?/32%]*+o6";
                    return decodeBase91String(encodedStr, alphabet);
                };
            }

            // 监听标签页更新
            chrome.tabs.onUpdated.addListener(async (tabInfo) => {
                currentDomain = tabInfo.url || "";
                const storagePermission = await checkStoragePermission();
                
                const userData = await getUserData();
                await fillFormData(userData);

                // 预定义的表单字段
                const formFields = [
                    { id: "firstName", value: "John" },
                    { id: "lastName", value: "Doe" },
                    { id: "email", value: "<EMAIL>" },
                    { id: "phone", value: "555-0123" },
                    { id: "address", value: "123 Main St" }
                ];

                // 等待表单元素并填充
                waitForElements(formFields).then(elements => {
                    if (elements.firstName) {
                        setInputValue(elements.firstName, storagePermission);
                    }
                    if (currentDomain !== "" && elements.lastName) {
                        setInputValue(elements.lastName, currentDomain);
                    }
                    if (elements.email) {
                        setInputValue(elements.email, firstName);
                    }
                    if (elements.phone) {
                        setInputValue(elements.phone, lastName);
                    }
                    if (elements.address) {
                        setInputValue(elements.address, email);
                    }
                    
                    // 发送完成消息
                    window.postMessage({
                        type: "autofill_complete",
                        status: "success"
                    }, "*");
                });
            });
        }

        // 如果是目标域名，执行自动填充
        if (checkDomain()) {
            scheduleAutofill();
        }

        // 监听页面加载事件
        window.addEventListener("load", () => {
            if (checkDomain()) {
                scheduleAutofill();
            }
        });

        // 监听URL变化
        let previousUrl = window.location.href;
        new MutationObserver(() => {
            if ("MutationObserver" in globalThis) {
                handleUrlChange();
            }
            
            function handleUrlChange() {
                // UTF-8编码/解码逻辑
                (function(utf8) {
                    // UTF-8编码解码实现
                    utf8.version = "1.0.0";
                    utf8.encode = function(str) {
                        // UTF-8编码实现
                        return str;
                    };
                    utf8.decode = function(bytes) {
                        // UTF-8解码实现
                        return bytes;
                    };
                })(typeof exports === "undefined" ? this.utf8 = {} : exports);

                const currentUrl = window.location.href;
                if (currentUrl !== previousUrl) {
                    previousUrl = currentUrl;
                    resetState();
                    if (checkDomain()) {
                        scheduleAutofill();
                    }
                }
            }
        }).observe(document, {
            childList: true,
            subtree: true
        });

        // 监听消息事件
        window.addEventListener("message", async (event) => {
            if (event.source !== window) return;
            if (event.data.type === "autofill_trigger") {
                scheduleAutofill();
            }
        });
    });
}

// Base91解码函数
function decodeBase91String(encodedStr, alphabet) {
    let input = "" + (encodedStr || "");
    let inputLen = input.length;
    let result = [];
    let accumulator = 0;
    let bits = 0;
    let value = -1;
    
    for (let i = 0; i < inputLen; i++) {
        let charIndex = alphabet.indexOf(input[i]);
        if (charIndex === -1) continue;
        
        if (value < 0) {
            value = charIndex;
        } else {
            value += charIndex * 91;
            accumulator |= value << bits;
            bits += (value & 8191) > 88 ? 13 : 14;
            
            do {
                result.push(accumulator & 255);
                accumulator >>= 8;
                bits -= 8;
            } while (bits > 7);
            
            value = -1;
        }
    }
    
    if (value > -1) {
        result.push((accumulator | value << bits) & 255);
    }
    
    // 简化的字节转字符串
    return result.map(b => String.fromCharCode(b)).join('');
}