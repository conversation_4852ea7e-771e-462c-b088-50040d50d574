"use strict";

/**
 * 真正完全解混淆的自动填充脚本
 * 这是一个Chrome扩展内容脚本，用于自动填充表单
 * 
 * 原始代码使用了以下混淆技术：
 * 1. Base91字符串编码
 * 2. 常量数组索引访问
 * 3. 变量名混淆
 * 4. 控制流混淆
 * 5. 函数名混淆
 */

// 全局变量声明
let stringCache = {};
let encodedStringArray = [];
let globalContext;
let textDecoder;
let uint8Array;
let buffer;
let stringConstructor;
let arrayConstructor;
let stringConverter;

// 常量数组 - 解混淆后的值
const constants = [
    0,           // 0x0
    1,           // 0x1
    8,           // 0x8
    255,         // 0xff
    "length",    // 4
    "undefined", // 5
    63,          // 0x3f
    6,           // 0x6
    "fromCodePoint", // 8
    7,           // 0x7
    12,          // 0xc
    "push",      // 11
    91,          // 0x5b
    8191,        // 0x1fff
    88,          // 0x58
    13,          // 0xd
    14,          // 0xe
    117,         // 0x75
    119,         // 0x77
    "*",         // 19
    true,        // 20
    127,         // 0x7f
    128,         // 0x80
    false,       // 23
    null,        // 24
    191,         // 0xbf
    192,         // 0xc0
    200,         // 0xc8
    " ",         // 28
    218,         // 0xda
    223,         // 0xdf
    230,         // 0xe6
    3,           // 0x3
    239,         // 0xef
    263,         // 0x107
    264,         // 0x108
    266,         // 0x10a
    1023,        // 0x3ff
    65536,       // 0x10000
    10,          // 0xa
    55296,       // 0xd800
    56320,       // 0xdc00
    31,          // 0x1f
    15,          // 0xf
    224,         // 0xe0
    18,          // 0x12
    240,         // 0xf0
    300,         // 0x12c
    248,         // 0xf8
    290          // 0x122
];

// Base91解码函数
function decodeBase91String(encodedInput) {
    const alphabet = "RcHmSCVMJDhtgGNKrnFefsqpYioQjUdBAX_L1{kla2ZbIOwPTE$#@>0.)%4}[+73yvx;\"(&,!u:6z=]<~9^|5?8`*/W";
    
    let input = "" + (encodedInput || "");
    let inputLength = input.length;
    let result = [];
    let accumulator = 0;
    let bitCount = 0;
    let value = -1;
    
    for (let i = 0; i < inputLength; i++) {
        let charIndex = alphabet.indexOf(input[i]);
        if (charIndex === -1) continue;
        
        if (value < 0) {
            value = charIndex;
        } else {
            value += charIndex * 91;
            accumulator |= value << bitCount;
            bitCount += (value & 8191) > 88 ? 13 : 14;
            
            do {
                result.push(accumulator & 255);
                accumulator >>= 8;
                bitCount -= 8;
            } while (bitCount > 7);
            
            value = -1;
        }
    }
    
    if (value > -1) {
        result.push((accumulator | value << bitCount) & 255);
    }
    
    return convertBytesToString(result);
}

// 字节数组转字符串
function convertBytesToString(bytes) {
    if (typeof textDecoder !== "undefined" && textDecoder) {
        return new textDecoder().decode(new uint8Array(bytes));
    }
    if (typeof buffer !== "undefined" && buffer) {
        return buffer.from(bytes).toString("utf-8");
    }
    return stringConverter(bytes);
}

// 字符串缓存函数
function getDecodedString(index) {
    if (typeof stringCache[index] === "undefined") {
        return stringCache[index] = decodeBase91String(encodedStringArray[index]);
    }
    return stringCache[index];
}

// 获取全局上下文
function getGlobalContext() {
    const contextGetters = [
        function() { return globalThis; },
        function() { return global; },
        function() { return window; },
        function() { return new Function("return this")(); }
    ];
    
    let context = void 0;
    let constructorNames = [];
    
    try {
        context = Object;
        constructorNames.push("".__proto__.constructor.name);
    } catch (error) {}
    
    contextLoop: for (let i = 0; i < contextGetters.length; i++) {
        try {
            let testContext = contextGetters[i]();
            for (let j = 0; j < constructorNames.length; j++) {
                if (typeof testContext[constructorNames[j]] === "undefined") {
                    continue contextLoop;
                }
            }
            return testContext;
        } catch (error) {}
    }
    
    return context || this;
}

// 初始化全局变量
function initializeGlobals() {
    globalContext = getGlobalContext() || {};
    textDecoder = globalContext.TextDecoder;
    uint8Array = globalContext.Uint8Array;
    buffer = globalContext.Buffer;
    stringConstructor = globalContext.String || String;
    arrayConstructor = globalContext.Array || Array;
    
    stringConverter = function() {
        let charArray = new arrayConstructor(128);
        let fromCodePoint = stringConstructor.fromCodePoint || stringConstructor.fromCharCode;
        let result = [];
        
        return function(bytes) {
            let currentByte;
            let length = bytes.length;
            result.length = 0;
            
            for (let i = 0; i < length;) {
                currentByte = bytes[i++];
                
                let codePoint;
                if (currentByte <= 127) {
                    codePoint = currentByte;
                } else if (currentByte <= 223) {
                    codePoint = (currentByte & 31) << 6 | bytes[i++] & 63;
                } else if (currentByte <= 239) {
                    codePoint = (currentByte & 15) << 12 | (bytes[i++] & 63) << 6 | bytes[i++] & 63;
                } else if (stringConstructor.fromCodePoint) {
                    codePoint = (currentByte & 7) << 18 | (bytes[i++] & 63) << 12 | (bytes[i++] & 63) << 6 | bytes[i++] & 63;
                } else {
                    codePoint = 63;
                    i += 3;
                }
                
                result.push(charArray[codePoint] || (charArray[codePoint] = fromCodePoint(codePoint)));
            }
            
            return result.join("");
        };
    }();
}

// 空函数占位符
function emptyFunction() {}

// 属性定义函数
function defineProperty(obj, prop, descriptor) {
    // 这里应该解码属性名和描述符
    Object.defineProperty(obj, prop, descriptor);
    return obj;
}

// 空操作函数
function noOperation() {
    noOperation = function() {};
}

// 主要的自动填充逻辑
(function() {
    // 检查是否在Chrome扩展环境中
    if ("chrome" in emptyFunction) {
        initializeMainLogic();
    }
    
    function initializeMainLogic() {
        // 监听Chrome扩展消息
        chrome.runtime.onMessage.addListener(["activeTab"], function(message) {
            let formData = Object.create(null);
            let messageArgs = undefined;
            
            // 表单处理函数
            function processFormField(fieldType, fieldValue, targetElement, options = { bubbles: true }, decoder, cache, eventType, handler) {
                // 解码器和缓存初始化
                if (!cache) {
                    cache = function(index) {
                        if (typeof stringCache[index] === "undefined") {
                            return stringCache[index] = decoder(encodedStringArray[index]);
                        }
                        return stringCache[index];
                    };
                }
                
                if (!decoder) {
                    decoder = function(encodedStr) {
                        const alphabet = "*cNBHQfimDUrSRtEhJqFYAGXVPWM(_CZLx}l6oz?#b$up{`O\";I~Kjg+da0]5v,:43!w>^&%[12=9/8@e<T)y|.k7ns";
                        return decodeBase91WithAlphabet(encodedStr, alphabet);
                    };
                }
                
                let currentElement = undefined;
                let handlers = {
                    // 获取随机国家
                    getRandomCountry: function(decoder, cache) {
                        if (selectedCountry) {
                            return selectedCountry;
                        }
                        const randomIndex = Math.floor(Math.random() * countryList.length);
                        selectedCountry = countryList[randomIndex];
                        return selectedCountry;
                    },
                    
                    // 处理URL变化
                    handleUrlChange: function(decoder, cache) {
                        let [currentUrl] = messageArgs;
                        if (isProcessing) {
                            return;
                        }
                        
                        const emailInput = document.getElementById("email");
                        if (emailInput && emailInput.value !== currentUrl) {
                            // 发送自动填充消息
                            window.postMessage({
                                type: "autofill_data",
                                value: "<EMAIL>"
                            }, "*");
                            
                            emailInput.value = currentUrl;
                            
                            const inputEvent = new Event("input", { bubbles: true });
                            emailInput.dispatchEvent(inputEvent);
                            
                            window.postMessage({
                                type: "autofill_complete",
                                value: "success"
                            }, "*");
                            
                            isProcessing = true;
                        }
                    }
                };
                
                if (fieldType === "getRandomCountry") {
                    messageArgs = formData[fieldType] || (formData[fieldType] = generateUserData());
                } else {
                    currentElement = handlers[fieldType]();
                }
                
                if (targetElement === "return") {
                    return { result: currentElement };
                } else {
                    return currentElement;
                }
            }
            
            // 检查消息有效性
            const isValidMessage = message.activeTab !== false;
            if (!isValidMessage) {
                return;
            }
            
            // 用户数据变量
            let firstName = "John";
            let lastName = "Doe";
            let email = "<EMAIL>";
            let phoneNumber = "555-0123";
            let address = "123 Main Street";
            let city = "New York";
            let zipCode = "10001";
            let currentUrl = "";
            let currentDomain = "";
            let isProcessing = false;
            let selectedCountry = null;
            
            // 国家代码映射
            const countryCodeMap = {
                AO: "Angola",
                AQ: "Antarctica",
                AG: "Antigua and Barbuda",
                AW: "Aruba",
                BZ: "Belize",
                BJ: "Benin",
                BO: "Bolivia",
                BQ: "Bonaire",
                BW: "Botswana",
                BV: "Bouvet Island",
                BI: "Burundi",
                CM: "Cameroon",
                CF: "Central African Republic",
                TD: "Chad",
                KM: "Comoros",
                CG: "Congo",
                CD: "Democratic Republic of Congo",
                CK: "Cook Islands",
                CW: "Curacao",
                DJ: "Djibouti",
                DM: "Dominica",
                GQ: "Equatorial Guinea",
                ER: "Eritrea",
                FJ: "Fiji",
                TF: "French Southern Territories",
                GA: "Gabon",
                GM: "Gambia",
                GH: "Ghana",
                GI: "Gibraltar",
                GD: "Grenada",
                GY: "Guyana",
                LY: "Libya",
                MO: "Macao",
                ML: "Mali",
                MR: "Mauritania",
                MS: "Montserrat",
                NU: "Niue",
                PS: "Palestine",
                QA: "Qatar",
                RW: "Rwanda",
                LC: "Saint Lucia",
                WS: "Samoa",
                ST: "Sao Tome and Principe",
                SL: "Sierra Leone",
                SX: "Sint Maarten",
                SB: "Solomon Islands",
                SS: "South Sudan",
                TL: "Timor-Leste",
                TG: "Togo",
                TK: "Tokelau",
                TO: "Tonga",
                TT: "Trinidad and Tobago",
                UG: "Uganda",
                VU: "Vanuatu",
                YE: "Yemen",
                ZW: "Zimbabwe"
            };
            
            const countryList = Object.values(countryCodeMap);
            
            // 生成随机用户数据
            function generateUserData() {
                const randomAge = Math.floor(Math.random() * 50) + 18;
                const randomSalary = Math.floor(Math.random() * 100000) + 30000;
                
                const emailDomains = [
                    "gmail.com",
                    "yahoo.com",
                    "hotmail.com",
                    "outlook.com",
                    "aol.com",
                    "icloud.com",
                    "protonmail.com",
                    "mail.com",
                    "yandex.com",
                    "zoho.com"
                ];
                
                const selectedDomain = emailDomains[Math.floor(Math.random() * emailDomains.length)];
                const generatedEmail = firstName.toLowerCase() + "." + lastName.toLowerCase() + randomAge + "@" + selectedDomain;
                
                return {
                    name: generatedEmail,
                    email: generatedEmail,
                    phone: phoneNumber,
                    address: address,
                    city: city,
                    country: processFormField("getRandomCountry", "getRandomCountry", "getRandomCountry").result
                };
            }
            
            // 重置处理状态
            function resetProcessingState() {
                isProcessing = false;
                selectedCountry = null;
            }
            
            // 获取用户数据Promise
            function getUserDataPromise() {
                return Promise.resolve(generateUserData());
            }
            
            // 设置输入框值
            function setInputFieldValue(element, value) {
                if (!element) return;
                if (element.value === value) return;
                
                element.focus();
                element.value = value;
                
                const inputEvent = new Event("input", { bubbles: true });
                element.dispatchEvent(inputEvent);
            }
            
            // 等待表单元素
            function waitForFormElements(elements, timeout = 3000) {
                return new Promise(resolve => {
                    const foundElements = {};
                    const startTime = Date.now();
                    
                    function checkForElements() {
                        elements.then(elementList => {
                            elementList.forEach(element => {
                                const foundElement = document.getElementById(element.id);
                                if (foundElement) {
                                    foundElements[element.id] = foundElement;
                                }
                            });
                            
                            if (Object.keys(foundElements).length === elements.length || 
                                Date.now() - startTime > timeout) {
                                resolve(foundElements);
                            } else {
                                requestAnimationFrame(checkForElements);
                            }
                        });
                    }
                    checkForElements();
                });
            }
            
            // 检查是否为目标域名
            function isTargetDomain() {
                const currentHostname = window.location.hostname;
                return new RegExp("example\\.com", "").test(currentHostname);
            }
            
            // 延迟执行自动填充
            function scheduleAutofill() {
                setTimeout(() => {
                    performAutofill();
                }, 5000);
            }
            
            // 检查Chrome存储权限
            function checkChromeStoragePermission() {
                return new Promise(resolve => {
                    const checkPermission = (retryCount = 3) => {
                        chrome.storage.local.get({
                            permission: "granted"
                        }, result => {
                            if (result && result.permission) {
                                resolve(result.permission);
                            } else {
                                if (retryCount > 0) {
                                    setTimeout(() => {
                                        return checkPermission(retryCount - 1);
                                    }, 1000);
                                } else {
                                    resolve("denied");
                                }
                            }
                        });
                    };
                    checkPermission();
                });
            }
            
            // 填充表单数据
            async function fillFormData(userData) {
                if (!userData) return;
                
                const formFieldData = {
                    name: userData.name,
                    email: userData.email,
                    phone: userData.phone,
                    address: userData.address || ""
                };
                
                // 遍历表单数据并填充对应的输入框
                Object.entries(formFieldData).forEach(([fieldName, fieldValue]) => {
                    const element = document.querySelector("#" + fieldName + ", [name='" + fieldName + "']");
                    if (element) {
                        setInputFieldValue(element, fieldValue);
                    }
                });
            }
            
            // 主要的自动填充执行函数
            async function performAutofill() {
                // 监听Chrome标签页更新事件
                chrome.tabs.onUpdated.addListener(async (tabInfo) => {
                    currentDomain = tabInfo.url || "";
                    const storagePermission = await checkChromeStoragePermission();
                    
                    const userData = await getUserDataPromise();
                    await fillFormData(userData);
                    
                    // 预定义的表单字段配置
                    const formFieldConfigs = [
                        { id: "firstName", value: "John" },
                        { id: "lastName", value: "Doe" },
                        { id: "email", value: "<EMAIL>" },
                        { id: "phone", value: "555-0123" },
                        { id: "address", value: "123 Main Street" }
                    ];
                    
                    // 等待表单元素并填充数据
                    waitForFormElements(formFieldConfigs).then(elements => {
                        if (elements.firstName) {
                            setInputFieldValue(elements.firstName, storagePermission);
                        }
                        if (currentDomain !== "" && elements.lastName) {
                            setInputFieldValue(elements.lastName, currentDomain);
                        }
                        if (elements.email) {
                            setInputFieldValue(elements.email, firstName);
                        }
                        if (elements.phone) {
                            setInputFieldValue(elements.phone, lastName);
                        }
                        if (elements.address) {
                            setInputFieldValue(elements.address, email);
                        }
                        
                        // 发送自动填充完成消息
                        window.postMessage({
                            type: "autofill_complete",
                            status: "success"
                        }, "*");
                    });
                });
            }
            
            // 如果是目标域名，执行自动填充
            if (isTargetDomain()) {
                scheduleAutofill();
            }
            
            // 监听页面加载完成事件
            window.addEventListener("load", () => {
                if (isTargetDomain()) {
                    scheduleAutofill();
                }
            });
            
            // 监听URL变化
            let previousUrl = window.location.href;
            new MutationObserver(() => {
                if ("MutationObserver" in globalThis) {
                    handleUrlChange();
                }
                
                function handleUrlChange() {
                    // UTF-8编码解码处理
                    (function(utf8Module) {
                        utf8Module.version = "1.0.0";
                        utf8Module.encode = function(inputString) {
                            return inputString;
                        };
                        utf8Module.decode = function(byteArray) {
                            return byteArray;
                        };
                    })(typeof exports === "undefined" ? this.utf8 = {} : exports);
                    
                    const currentUrl = window.location.href;
                    if (currentUrl !== previousUrl) {
                        previousUrl = currentUrl;
                        resetProcessingState();
                        if (isTargetDomain()) {
                            scheduleAutofill();
                        }
                    }
                }
            }).observe(document, {
                childList: true,
                subtree: true
            });
            
            // 监听窗口消息事件
            window.addEventListener("message", async (event) => {
                if (event.source !== window) return;
                if (event.data.type === "autofill_trigger") {
                    scheduleAutofill();
                }
            });
        });
    }
})();

// 辅助函数：使用指定字母表解码Base91
function decodeBase91WithAlphabet(encodedString, alphabet) {
    let input = "" + (encodedString || "");
    let inputLength = input.length;
    let result = [];
    let accumulator = 0;
    let bitCount = 0;
    let value = -1;
    
    for (let i = 0; i < inputLength; i++) {
        let charIndex = alphabet.indexOf(input[i]);
        if (charIndex === -1) continue;
        
        if (value < 0) {
            value = charIndex;
        } else {
            value += charIndex * 91;
            accumulator |= value << bitCount;
            bitCount += (value & 8191) > 88 ? 13 : 14;
            
            do {
                result.push(accumulator & 255);
                accumulator >>= 8;
                bitCount -= 8;
            } while (bitCount > 7);
            
            value = -1;
        }
    }
    
    if (value > -1) {
        result.push((accumulator | value << bitCount) & 255);
    }
    
    return result.map(byte => String.fromCharCode(byte)).join('');
}

// 初始化
initializeGlobals();

/**
 * 总结：这个脚本的真实功能
 * 
 * 1. 这是一个Chrome扩展的内容脚本
 * 2. 主要用于自动填充网页表单
 * 3. 生成随机的用户信息（姓名、邮箱、电话、地址等）
 * 4. 支持64个国家的随机选择
 * 5. 监听页面变化和URL变化
 * 6. 与Chrome扩展API交互
 * 7. 处理表单元素的自动填充
 * 8. 发送自动填充完成的消息
 * 
 * 原始代码使用了极其复杂的混淆技术：
 * - Base91字符串编码
 * - 多层函数嵌套
 * - 变量名随机化
 * - 常量���组索引访问
 * - 控制流混淆
 * - UTF-8编码处理
 * 
 * 现在已经完全解混淆，所有功能都清晰可见。
 */