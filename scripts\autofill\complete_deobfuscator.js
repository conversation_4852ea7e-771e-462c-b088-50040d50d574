// 完全解混淆工具 - 逐步解析原始混淆代码

// 第一步：解析常量数组
const constants = [
    0x0,        // 0
    0x1,        // 1  
    0x8,        // 8
    0xff,       // 255
    "length",   // 4
    "undefined", // 5
    0x3f,       // 63
    0x6,        // 6
    "fromCodePoint", // 8
    0x7,        // 7
    0xc,        // 12
    "push",     // 11
    0x5b,       // 91
    0x1fff,     // 8191
    0x58,       // 88
    0xd,        // 13
    0xe,        // 14
    0x75,       // 117
    0x77,       // 119
    "*",        // 19
    true,       // 20
    0x7f,       // 127
    0x80,       // 128
    false,      // 23
    null,       // 24
    0xbf,       // 191
    0xc0,       // 192
    0xc8,       // 200
    " ",        // 28
    0xda,       // 218
    0xdf,       // 223
    0xe6,       // 230
    0x3,        // 3
    0xef,       // 239
    0x107,      // 263
    0x108,      // 264
    0x10a,      // 266
    0x3ff,      // 1023
    0x10000,    // 65536
    0xa,        // 10
    0xd800,     // 55296
    0xdc00,     // 56320
    0x1f,       // 31
    0xf,        // 15
    0xe0,       // 224
    0x12,       // 18
    0xf0,       // 240
    0x12c,      // 300
    0xf8,       // 248
    0x122       // 290
];

// 第二步：Base91解码器
function decodeBase91(input, alphabet) {
    let str = "" + (input || "");
    let len = str.length;
    let output = [];
    let accumulator = 0;
    let bits = 0;
    let value = -1;
    
    for (let i = 0; i < len; i++) {
        let charIndex = alphabet.indexOf(str[i]);
        if (charIndex === -1) continue;
        
        if (value < 0) {
            value = charIndex;
        } else {
            value += charIndex * 91;
            accumulator |= value << bits;
            bits += (value & 8191) > 88 ? 13 : 14;
            
            do {
                output.push(accumulator & 255);
                accumulator >>= 8;
                bits -= 8;
            } while (bits > 7);
            
            value = -1;
        }
    }
    
    if (value > -1) {
        output.push((accumulator | value << bits) & 255);
    }
    
    return bytesToString(output);
}

// UTF-8字节转字符串
function bytesToString(bytes) {
    let result = "";
    for (let i = 0; i < bytes.length; i++) {
        let byte = bytes[i];
        if (byte <= 127) {
            result += String.fromCharCode(byte);
        } else if (byte <= 223) {
            result += String.fromCharCode(((byte & 31) << 6) | (bytes[++i] & 63));
        } else if (byte <= 239) {
            result += String.fromCharCode(((byte & 15) << 12) | ((bytes[++i] & 63) << 6) | (bytes[++i] & 63));
        } else {
            // 4字节UTF-8字符
            let codePoint = ((byte & 7) << 18) | ((bytes[++i] & 63) << 12) | ((bytes[++i] & 63) << 6) | (bytes[++i] & 63);
            result += String.fromCodePoint(codePoint);
        }
    }
    return result;
}

// 第三步：解码所有字符串
const alphabet = "RcHmSCVMJDhtgGNKrnFefsqpYioQjUdBAX_L1{kla2ZbIOwPTE$#@>0.)%4}[+73yvx;\"(&,!u:6z=]<~9^|5?8`*/W";

const encodedStrings = [
    "LTv0H8T@!vo(^(.LnbVMR_hYd3Uz@S$a`&{:C>OPYf|;&NQ1iKm",
    "[_o#c5J@)G2.XSVB77qBoaXU^K9&cTSd!TD6%[vc:}c{gKe_gc",
    "CgWJ^@5aQ}N!c",
    "Y:kz&7+PN}?&Km@_sv20mTxgGNgGSKd1<Vfh!$IY(0YGzQmnrc",
    "avMD4=~gE%3EtkRFq\"4#/]mGHKomlHV",
    "b,/Jl3)Hb7B7TQ:o(TyuU",
    "o[!#T#=(wJ,chC)2ET_Q~9hw1e.~*,;{$TU.(.&8_[5=]Hr_9{H",
    "$gRd@5UPVh<E_GWjeU[>$)4HI}f<3Q$a9v6=lLq@3[o",
    "7vm=C1,#ZN",
    "f0>jZ0%3&4<3sT(YMo5$Y]i2w)}~rPIen#`u%whF9[`wc",
    "h,UB0)R",
    "Q_kMx[~w<[cG4{WrxmAD|$7wKFj(R",
    "kpWJDTOc",
    "s\"xJc9<#PxcH2rtf7j?$}=(MPF+N1PH1hgq.P#[YN3N<c!VFut,Se",
    ".m_!CT|GKxhmOrB1C:q@I^/23FL(?1*_#,d@}wNH",
    "`&6:yw=G?0.",
    "3vqj{?~YRDW&~S",
    "}0wD8(seS0Oj\"mN",
    "Em,jK1oHg}99jPg",
    "IV.E>4FY[>V#2T&{y}s.vlrwfG",
    "A0qMJzjgCeI~+iDe}p+:4_]aWnB!JK.Ucg&.ia:_S",
    "d%>.X~5_=Nn^|o._swr6Y@qw^%B!q{TQ~7Xd5Oyd#%yuvGK",
    "7}.u#3]@&4H!*K1{y_gh}_h1AF;=8&BZL0nQt|_@4f:Lt$hopA=$~,zFm",
    "a7jhS<Be/f8$ErHp",
    "^_|=vw.6rg\"&oEv{cU4Jv1NJS01!KHcB[7qEGTd@NsS<$^;i~uUh",
    "Lbk.;w:V_DbhLHq2y}H",
    "]nJdC(u2/7ajrP)ZT%uA*|c>4fXYaP%U7upS",
    "~}F6.wOG27/At^G",
    "_\"H=9Xw@HD^FxiGQ[}5$*|aa(yZT@},Bx\"rVY,PL~>A",
    ")#p]lPwgafj!XG}j@meAI~5YV.I",
    "1\"N@^(0\"7F",
    "F[XVX,dFyDxAXS",
    "r0s@!__gZN",
    "cb3Eclc2Dvdz>+c1^jxJy5>L,f{_5PDeswfhw{:F#yKdTmGo<v:J",
    "RDZA!<agExYGM^bo@pl@\"_?GvF[MLoHYo0H",
    "[T/0>l4:}t",
    ",D2dK%XL[)Lh)Q+nLor0J<wF}NS<y}2eg,A#\"[W8unxFSKPla#7DQ]e>V",
    "#,+Smw|U~>L!%!;n9{F#)4|M,y.}#N%j\"7%V",
    "gE,SM8IH",
    "[Do63lvG3JWJL4!ei:(oK:aw9KBzZNZeLDJDp?R",
    "z}KX]Tp(=t<>cT%j^#H",
    ".fpz^$}G~3@xoEC{~<EMAIL>+\"k@v?oi|]gKxZ@Bk1dHU`u|7R",
    "!)o!a)Xe]J<KD[2edam",
    "7{Nz`]*_}tuAPNUnk0w.3w$H",
    "ZnjUuTt6}vxu!K&{\"miVJz0:rFT(MKiUz}0@f$R1x0(us1^_EoVM",
    "iv,Dc!r1ov0BKN+1e#=o;:IyeD)~ArLFaT8S",
    "^OzCia;punuUBk8a|fwS3w>c",
    "6#>M]%Pev3_!8N$jY,.@|7GcyK/yd+@a{#[>\"!k4tn",
    "?V>M7>/%M",
    "7m9V>!0Hb)Jmc",
    "n,>BD9SFuf0~!Q<Qio]zJ<z4KK,uz!*q$v!D:%NH90aj`om{xumUWOwFS"
];

// 解码前50个字符串
console.log("=== 解码字符串 ===");
for (let i = 0; i < Math.min(50, encodedStrings.length); i++) {
    try {
        const decoded = decodeBase91(encodedStrings[i], alphabet);
        console.log(`${i.toString().padStart(2, '0')}: "${decoded}"`);
    } catch (e) {
        console.log(`${i.toString().padStart(2, '0')}: 解码失败 - ${e.message}`);
    }
}

// 分析关键索引
console.log("\n=== 关键常量分析 ===");
console.log(`constants[0x61] = ${constants[0x61] || '未定义'}`);
console.log(`constants[0x62] = ${constants[0x62] || '未定义'}`);
console.log(`constants[0x65] = ${constants[0x65] || '未定义'}`);
console.log(`constants[0x66] = ${constants[0x66] || '未定义'}`);
console.log(`constants[0x67] = ${constants[0x67] || '未定义'}`);
console.log(`constants[0x68] = ${constants[0x68] || '未定义'}`);
console.log(`constants[0x69] = ${constants[0x69] || '未定义'}`);

// 尝试手动解码一些关键字符串
const testStrings = [
    "chrome",
    "runtime", 
    "onMessage",
    "addListener",
    "activeTab",
    "document",
    "getElementById",
    "value",
    "input",
    "focus"
];

console.log("\n=== 可能的API调用 ===");
testStrings.forEach(str => {
    console.log(`"${str}"`);
});

// 分析代码结构
console.log("\n=== 代码结构分析 ===");
console.log("1. 这是一个Chrome扩展内容脚本");
console.log("2. 使用Base91编码混淆字符串");
console.log("3. 包含表单自动填充功能");
console.log("4. 监听DOM变化和消息事件");
console.log("5. 包含国家代码数据");
console.log("6. 使用UTF-8编码处理");
console.log("7. 实现了复杂的字符串缓存机制");

export { decodeBase91, bytesToString, constants, encodedStrings };