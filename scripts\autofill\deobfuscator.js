// 解混淆工具 - 用于分析和解开混淆的JavaScript代码

// 首先分析常量数组 tVCor5
const tVCor5 = [
    0x0,        // 0
    0x1,        // 1  
    0x8,        // 8
    0xff,       // 255
    "length",   // 4
    "undefined", // 5
    0x3f,       // 63
    0x6,        // 6
    "fromCodePoint", // 8
    0x7,        // 7
    0xc,        // 12
    "push",     // 11
    0x5b,       // 91
    0x1fff,     // 8191
    0x58,       // 88
    0xd,        // 13
    0xe,        // 14
    0x75,       // 117
    0x77,       // 119
    "*",        // 19
    true,       // 20
    0x7f,       // 127
    0x80,       // 128
    false,      // 23
    null,       // 24
    0xbf,       // 191
    0xc0,       // 192
    0xc8,       // 200
    " ",        // 28
    0xda,       // 218
    0xdf,       // 223
    0xe6,       // 230
    0x3,        // 3
    0xef,       // 239
    0x107,      // 263
    0x108,      // 264
    0x10a,      // 266
    0x3ff,      // 1023
    0x10000,    // 65536
    0xa,        // 10
    0xd800,     // 55296
    0xdc00,     // 56320
    0x1f,       // 31
    0xf,        // 15
    0xe0,       // 224
    0x12,       // 18
    0xf0,       // 240
    0x12c,      // 300
    0xf8,       // 248
    0x122       // 290
];

// 解码函数 - 这是核心的字符串解码逻辑
function decodeString(encodedStr) {
    const alphabet = "RcHmSCVMJDhtgGNKrnFefsqpYioQjUdBAX_L1{kla2ZbIOwPTE$#@>0.)%4}[+73yvx;\"(&,!u:6z=]<~9^|5?8`*/W";
    
    let input = "" + (encodedStr || "");
    let inputLen = input.length;
    let result = [];
    let accumulator = 0;
    let bits = 0;
    let value = -1;
    
    for (let i = 0; i < inputLen; i++) {
        let charIndex = alphabet.indexOf(input[i]);
        if (charIndex === -1) continue;
        
        if (value < 0) {
            value = charIndex;
        } else {
            value += charIndex * 91;
            accumulator |= value << bits;
            bits += (value & 8191) > 88 ? 13 : 14;
            
            do {
                result.push(accumulator & 255);
                accumulator >>= 8;
                bits -= 8;
            } while (bits > 7);
            
            value = -1;
        }
    }
    
    if (value > -1) {
        result.push((accumulator | value << bits) & 255);
    }
    
    return bytesToString(result);
}

// 字节数组转字符串
function bytesToString(bytes) {
    // 简���的UTF-8解码
    let result = "";
    for (let i = 0; i < bytes.length; i++) {
        if (bytes[i] < 128) {
            result += String.fromCharCode(bytes[i]);
        } else {
            // 处理UTF-8多字节字符
            result += String.fromCharCode(bytes[i]);
        }
    }
    return result;
}

// 解码字符串数组
const encodedStrings = [
    "LTv0H8T@!vo(^(.LnbVMR_hYd3Uz@S$a`&{:C>OPYf|;&NQ1iKm",
    "[_o#c5J@)G2.XSVB77qBoaXU^K9&cTSd!TD6%[vc:}c{gKe_gc",
    "CgWJ^@5aQ}N!c",
    "Y:kz&7+PN}?&Km@_sv20mTxgGNgGSKd1<Vfh!$IY(0YGzQmnrc",
    "avMD4=~gE%3EtkRFq\"4#/]mGHKomlHV",
    "b,/Jl3)Hb7B7TQ:o(TyuU",
    "o[!#T#=(wJ,chC)2ET_Q~9hw1e.~*,;{$TU.(.&8_[5=]Hr_9{H",
    "$gRd@5UPVh<E_GWjeU[>$)4HI}f<3Q$a9v6=lLq@3[o",
    "7vm=C1,#ZN",
    "f0>jZ0%3&4<3sT(YMo5$Y]i2w)}~rPIen#`u%whF9[`wc"
];

console.log("解码前10个字符串:");
for (let i = 0; i < Math.min(10, encodedStrings.length); i++) {
    try {
        const decoded = decodeString(encodedStrings[i]);
        console.log(`${i}: "${encodedStrings[i]}" -> "${decoded}"`);
    } catch (e) {
        console.log(`${i}: 解码失败 - ${e.message}`);
    }
}

// 分析代码结构
console.log("\n=== 代码结构分析 ===");
console.log("1. tVCor5数组包含常量值和字符串");
console.log("2. TnFFvTp函数是字符串解码器");
console.log("3. SkToto函数是字符串缓存机制");
console.log("4. 主要功能在chrome.runtime.onMessage.addListener中");
console.log("5. 包含表单自动填充逻辑");
console.log("6. 使用MutationObserver监听DOM变化");
console.log("7. 包含国家代码映射");

// 尝试解码一些关键字符串
const keyStrings = [
    "chrome",
    "runtime", 
    "onMessage",
    "addListener",
    "activeTab",
    "document",
    "getElementById",
    "value",
    "input",
    "focus",
    "dispatchEvent"
];

console.log("\n=== 可能的关键字符串 ===");
keyStrings.forEach(str => {
    console.log(`"${str}"`);
});