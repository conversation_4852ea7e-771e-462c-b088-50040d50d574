<!DOCTYPE html>
<html>
<head>
    <title>解混淆工具</title>
</head>
<body>
    <h1>JavaScript混淆代码解析</h1>
    <div id="output"></div>
    
    <script>
        // Base91解码器
        function decodeBase91(input, alphabet) {
            let str = "" + (input || "");
            let len = str.length;
            let output = [];
            let accumulator = 0;
            let bits = 0;
            let value = -1;
            
            for (let i = 0; i < len; i++) {
                let charIndex = alphabet.indexOf(str[i]);
                if (charIndex === -1) continue;
                
                if (value < 0) {
                    value = charIndex;
                } else {
                    value += charIndex * 91;
                    accumulator |= value << bits;
                    bits += (value & 8191) > 88 ? 13 : 14;
                    
                    do {
                        output.push(accumulator & 255);
                        accumulator >>= 8;
                        bits -= 8;
                    } while (bits > 7);
                    
                    value = -1;
                }
            }
            
            if (value > -1) {
                output.push((accumulator | value << bits) & 255);
            }
            
            return bytesToString(output);
        }

        // UTF-8字节转字符串
        function bytesToString(bytes) {
            let result = "";
            for (let i = 0; i < bytes.length; i++) {
                let byte = bytes[i];
                if (byte <= 127) {
                    result += String.fromCharCode(byte);
                } else if (byte <= 223) {
                    result += String.fromCharCode(((byte & 31) << 6) | (bytes[++i] & 63));
                } else if (byte <= 239) {
                    result += String.fromCharCode(((byte & 15) << 12) | ((bytes[++i] & 63) << 6) | (bytes[++i] & 63));
                } else {
                    let codePoint = ((byte & 7) << 18) | ((bytes[++i] & 63) << 12) | ((bytes[++i] & 63) << 6) | (bytes[++i] & 63);
                    result += String.fromCodePoint(codePoint);
                }
            }
            return result;
        }

        const alphabet = "RcHmSCVMJDhtgGNKrnFefsqpYioQjUdBAX_L1{kla2ZbIOwPTE$#@>0.)%4}[+73yvx;\"(&,!u:6z=]<~9^|5?8`*/W";

        const encodedStrings = [
            "LTv0H8T@!vo(^(.LnbVMR_hYd3Uz@S$a`&{:C>OPYf|;&NQ1iKm",
            "[_o#c5J@)G2.XSVB77qBoaXU^K9&cTSd!TD6%[vc:}c{gKe_gc",
            "CgWJ^@5aQ}N!c",
            "Y:kz&7+PN}?&Km@_sv20mTxgGNgGSKd1<Vfh!$IY(0YGzQmnrc",
            "avMD4=~gE%3EtkRFq\"4#/]mGHKomlHV",
            "b,/Jl3)Hb7B7TQ:o(TyuU",
            "o[!#T#=(wJ,chC)2ET_Q~9hw1e.~*,;{$TU.(.&8_[5=]Hr_9{H",
            "$gRd@5UPVh<E_GWjeU[>$)4HI}f<3Q$a9v6=lLq@3[o",
            "7vm=C1,#ZN",
            "f0>jZ0%3&4<3sT(YMo5$Y]i2w)}~rPIen#`u%whF9[`wc",
            "h,UB0)R",
            "Q_kMx[~w<[cG4{WrxmAD|$7wKFj(R",
            "kpWJDTOc",
            "s\"xJc9<#PxcH2rtf7j?$}=(MPF+N1PH1hgq.P#[YN3N<c!VFut,Se",
            ".m_!CT|GKxhmOrB1C:q@I^/23FL(?1*_#,d@}wNH",
            "`&6:yw=G?0.",
            "3vqj{?~YRDW&~S",
            "}0wD8(seS0Oj\"mN",
            "Em,jK1oHg}99jPg",
            "IV.E>4FY[>V#2T&{y}s.vlrwfG"
        ];

        let output = document.getElementById('output');
        output.innerHTML = '<h2>解码结果:</h2>';
        
        for (let i = 0; i < encodedStrings.length; i++) {
            try {
                const decoded = decodeBase91(encodedStrings[i], alphabet);
                output.innerHTML += `<p><strong>${i}:</strong> "${decoded}"</p>`;
                console.log(`${i}: "${decoded}"`);
            } catch (e) {
                output.innerHTML += `<p><strong>${i}:</strong> 解码失败 - ${e.message}</p>`;
                console.log(`${i}: 解码失败 - ${e.message}`);
            }
        }
    </script>
</body>
</html>