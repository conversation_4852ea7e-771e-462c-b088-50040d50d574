// Propaganda Auto Checkouter - Background Script (Deobfuscated)
"use strict";

// Global variables
let currentTabId = null;
let isProcessing = false;
let retryCount = 0;
let maxRetries = 5;

// Country codes mapping for random selection
const COUNTRY_CODES = {
    AO: "Angola", AQ: "Antarctica", AG: "Antigua and Barbuda", AW: "Aruba",
    BZ: "Belize", BJ: "Benin", BO: "Bolivia", BQ: "Bonaire",
    BW: "Botswana", BV: "Bouvet Island", BI: "Burundi", CM: "Cameroon",
    CF: "Central African Republic", TD: "Chad", KM: "Comoros", CG: "Congo",
    CD: "Democratic Republic of Congo", CK: "Cook Islands", CW: "Curaçao",
    DJ: "Djibouti", DM: "Dominica", GQ: "Equatorial Guinea", ER: "Eritrea",
    FJ: "Fiji", TF: "French Southern Territories", GA: "Gabon", GM: "Gambia",
    GH: "Ghana", GI: "Gibraltar", GD: "Grenada", GY: "Guyana", LY: "Libya",
    MO: "Macao", ML: "Mali", MR: "Mauritania", MS: "Montserrat",
    NU: "Niue", PS: "Palestine", QA: "Qatar", RW: "Rwanda", LC: "Saint Lucia",
    WS: "Samoa", ST: "São Tomé and Príncipe", SL: "Sierra Leone",
    SX: "Sint Maarten", SB: "Solomon Islands", SS: "South Sudan",
    TL: "Timor-Leste", TG: "Togo", TK: "Tokelau", TO: "Tonga",
    TT: "Trinidad and Tobago", UG: "Uganda", VU: "Vanuatu", YE: "Yemen", ZW: "Zimbabwe"
};

// Generate random card data
function generateCardData() {
    const cardNumbers = [
        "****************", "****************", "****************",
        "****************", "****************", "****************",
        "****************", "****************", "****************",
        "****************"
    ];
    
    const randomMonth = Math.floor(Math.random() * 12) + 1;
    const randomYear = Math.floor(Math.random() * 10) + 2025;
    const randomCard = cardNumbers[Math.floor(Math.random() * cardNumbers.length)];
    const randomCvv = Math.floor(Math.random() * 900) + 100;
    
    return {
        number: randomCard,
        expiry: `${randomMonth.toString().padStart(2, '0')}/${randomYear}`,
        cvv: randomCvv.toString(),
        name: "John Doe",
        country: getRandomCountry()
    };
}

// Get random country from the list
function getRandomCountry() {
    const countries = Object.values(COUNTRY_CODES);
    return countries[Math.floor(Math.random() * countries.length)];
}

// Check if current URL contains checkout
function isCheckoutPage() {
    return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                const url = tabs[0].url;
                resolve(url.includes('checkout') || url.includes('stripe') || url.includes('payment'));
            } else {
                resolve(false);
            }
        });
    });
}

// Auto-fill payment form
async function autoFillPaymentForm(cardData) {
    const fillScript = `
        (function() {
            const cardData = ${JSON.stringify(cardData)};
            
            // Find and fill card number
            const cardNumberSelectors = [
                'input[name*="card"]', 'input[id*="card"]', 'input[placeholder*="card"]',
                'input[data-testid*="card"]', '#card-number', '.card-number'
            ];
            
            for (let selector of cardNumberSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    element.value = cardData.number;
                    element.dispatchEvent(new Event('input', {bubbles: true}));
                    element.dispatchEvent(new Event('change', {bubbles: true}));
                    break;
                }
            }
            
            // Fill expiry date
            const expirySelectors = [
                'input[name*="expiry"]', 'input[name*="exp"]', 'input[placeholder*="MM/YY"]',
                '#expiry', '.expiry'
            ];
            
            for (let selector of expirySelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    element.value = cardData.expiry;
                    element.dispatchEvent(new Event('input', {bubbles: true}));
                    element.dispatchEvent(new Event('change', {bubbles: true}));
                    break;
                }
            }
            
            // Fill CVV
            const cvvSelectors = [
                'input[name*="cvv"]', 'input[name*="cvc"]', 'input[name*="security"]',
                '#cvv', '#cvc', '.cvv'
            ];
            
            for (let selector of cvvSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    element.value = cardData.cvv;
                    element.dispatchEvent(new Event('input', {bubbles: true}));
                    element.dispatchEvent(new Event('change', {bubbles: true}));
                    break;
                }
            }
            
            // Fill cardholder name
            const nameSelectors = [
                'input[name*="name"]', 'input[placeholder*="name"]',
                '#cardholder-name', '.cardholder-name'
            ];
            
            for (let selector of nameSelectors) {
                const element = document.querySelector(selector);
                if (element && element.type !== 'email') {
                    element.value = cardData.name;
                    element.dispatchEvent(new Event('input', {bubbles: true}));
                    element.dispatchEvent(new Event('change', {bubbles: true}));
                    break;
                }
            }
            
            return 'Payment form filled successfully';
        })();
    `;
    
    return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.executeScript(tabs[0].id, {code: fillScript}, (result) => {
                    resolve(result);
                });
            }
        });
    });
}

// Submit payment form
async function submitPaymentForm() {
    const submitScript = `
        (function() {
            const submitSelectors = [
                'button[type="submit"]', 'input[type="submit"]',
                'button[id*="submit"]', 'button[class*="submit"]',
                'button[id*="pay"]', 'button[class*="pay"]',
                '.submit-button', '#submit-button'
            ];
            
            for (let selector of submitSelectors) {
                const element = document.querySelector(selector);
                if (element && !element.disabled) {
                    element.click();
                    return 'Payment form submitted';
                }
            }
            
            return 'No submit button found';
        })();
    `;
    
    return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.executeScript(tabs[0].id, {code: submitScript}, (result) => {
                    resolve(result);
                });
            }
        });
    });
}

// Check for payment errors
async function checkPaymentStatus() {
    const checkScript = `
        (function() {
            const errorSelectors = [
                '.error', '.alert-error', '.payment-error',
                '[class*="error"]', '[id*="error"]',
                '.declined', '.failed'
            ];
            
            for (let selector of errorSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return {
                        hasError: true,
                        errorMessage: element.textContent.trim()
                    };
                }
            }
            
            // Check for success indicators
            const successSelectors = [
                '.success', '.payment-success', '.confirmation',
                '[class*="success"]', '[class*="complete"]'
            ];
            
            for (let selector of successSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return {
                        hasError: false,
                        isSuccess: true,
                        message: element.textContent.trim()
                    };
                }
            }
            
            return {
                hasError: false,
                isSuccess: false,
                message: 'Status unknown'
            };
        })();
    `;
    
    return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.executeScript(tabs[0].id, {code: checkScript}, (result) => {
                    resolve(result[0]);
                });
            }
        });
    });
}

// Main auto checkout process
async function startAutoCheckout() {
    if (isProcessing) {
        console.log('Auto checkout already in progress');
        return;
    }
    
    isProcessing = true;
    retryCount = 0;
    
    try {
        const isCheckout = await isCheckoutPage();
        if (!isCheckout) {
            console.log('Not on a checkout page');
            isProcessing = false;
            return;
        }
        
        await processPayment();
    } catch (error) {
        console.error('Auto checkout error:', error);
        isProcessing = false;
    }
}

// Process payment with retry logic
async function processPayment() {
    while (retryCount < maxRetries && isProcessing) {
        try {
            console.log(`Payment attempt ${retryCount + 1}/${maxRetries}`);
            
            // Generate new card data for each attempt
            const cardData = generateCardData();
            console.log('Generated card data:', cardData);
            
            // Fill the payment form
            await autoFillPaymentForm(cardData);
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
            
            // Submit the form
            await submitPaymentForm();
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds for processing
            
            // Check payment status
            const status = await checkPaymentStatus();
            console.log('Payment status:', status);
            
            if (status.isSuccess) {
                console.log('Payment successful!');
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icon.png',
                    title: 'Payment Success',
                    message: 'Payment completed successfully!'
                });
                break;
            } else if (status.hasError) {
                console.log('Payment failed:', status.errorMessage);
                retryCount++;
                
                if (retryCount < maxRetries) {
                    console.log(`Retrying in 3 seconds... (${retryCount}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } else {
                    console.log('Max retries reached');
                    chrome.notifications.create({
                        type: 'basic',
                        iconUrl: 'icon.png',
                        title: 'Payment Failed',
                        message: 'All payment attempts failed'
                    });
                }
            } else {
                console.log('Payment status unclear, retrying...');
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
        } catch (error) {
            console.error('Payment processing error:', error);
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    isProcessing = false;
}

// Listen for messages from popup or content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'startAutoCheckout') {
        startAutoCheckout();
        sendResponse({status: 'started'});
    } else if (request.action === 'stopAutoCheckout') {
        isProcessing = false;
        sendResponse({status: 'stopped'});
    } else if (request.action === 'getStatus') {
        sendResponse({
            isProcessing: isProcessing,
            retryCount: retryCount,
            maxRetries: maxRetries
        });
    }
});

// Auto-start when on checkout pages
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        if (tab.url.includes('checkout') || tab.url.includes('stripe') || tab.url.includes('payment')) {
            currentTabId = tabId;
            // Auto-start after 3 seconds delay
            setTimeout(() => {
                if (!isProcessing) {
                    startAutoCheckout();
                }
            }, 3000);
        }
    }
});

console.log('Propaganda Auto Checkouter background script loaded');
