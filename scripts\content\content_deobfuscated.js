// Propaganda Auto Checkouter - Content Script (Deobfuscated)
"use strict";

// Configuration
const CONFIG = {
    AUTO_FILL_DELAY: 1000,
    SUBMIT_DELAY: 2000,
    RETRY_DELAY: 3000,
    MAX_RETRIES: 5
};

// Card data generator
class CardGenerator {
    static CARD_NUMBERS = [
        "****************", "****************", "****************",
        "****************", "****************", "****************",
        "****************", "****************", "****************",
        "****************"
    ];
    
    static NAMES = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    ];
    
    static generate() {
        const randomCard = this.CARD_NUMBERS[Math.floor(Math.random() * this.CARD_NUMBERS.length)];
        const randomName = this.NAMES[Math.floor(Math.random() * this.NAMES.length)];
        const randomMonth = Math.floor(Math.random() * 12) + 1;
        const randomYear = Math.floor(Math.random() * 10) + 2025;
        const randomCvv = Math.floor(Math.random() * 900) + 100;
        
        return {
            number: randomCard,
            expiry: `${randomMonth.toString().padStart(2, '0')}/${randomYear}`,
            cvv: randomCvv.toString(),
            name: randomName
        };
    }
}

// Form field detector and filler
class FormFiller {
    static SELECTORS = {
        cardNumber: [
            'input[name*="card"]', 'input[id*="card"]', 'input[placeholder*="card"]',
            'input[data-testid*="card"]', '#card-number', '.card-number',
            'input[autocomplete="cc-number"]', '[data-elements-stable-field-name="cardNumber"]'
        ],
        expiry: [
            'input[name*="expiry"]', 'input[name*="exp"]', 'input[placeholder*="MM/YY"]',
            '#expiry', '.expiry', 'input[autocomplete="cc-exp"]',
            '[data-elements-stable-field-name="cardExpiry"]'
        ],
        cvv: [
            'input[name*="cvv"]', 'input[name*="cvc"]', 'input[name*="security"]',
            '#cvv', '#cvc', '.cvv', 'input[autocomplete="cc-csc"]',
            '[data-elements-stable-field-name="cardCvc"]'
        ],
        name: [
            'input[name*="name"]', 'input[placeholder*="name"]',
            '#cardholder-name', '.cardholder-name', 'input[autocomplete="cc-name"]'
        ],
        email: [
            'input[type="email"]', 'input[name*="email"]', '#email', '.email'
        ],
        submit: [
            'button[type="submit"]', 'input[type="submit"]',
            'button[id*="submit"]', 'button[class*="submit"]',
            'button[id*="pay"]', 'button[class*="pay"]',
            '.submit-button', '#submit-button', '[data-testid*="submit"]'
        ]
    };
    
    static findElement(selectors) {
        for (let selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) { // Check if visible
                return element;
            }
        }
        return null;
    }
    
    static fillField(element, value) {
        if (!element) return false;
        
        // Clear existing value
        element.value = '';
        element.focus();
        
        // Set new value
        element.value = value;
        
        // Trigger events
        const events = ['input', 'change', 'blur', 'keyup'];
        events.forEach(eventType => {
            element.dispatchEvent(new Event(eventType, { bubbles: true }));
        });
        
        return true;
    }
    
    static async fillForm(cardData) {
        const results = {};
        
        // Fill card number
        const cardNumberField = this.findElement(this.SELECTORS.cardNumber);
        results.cardNumber = this.fillField(cardNumberField, cardData.number);
        
        await this.delay(500);
        
        // Fill expiry
        const expiryField = this.findElement(this.SELECTORS.expiry);
        results.expiry = this.fillField(expiryField, cardData.expiry);
        
        await this.delay(500);
        
        // Fill CVV
        const cvvField = this.findElement(this.SELECTORS.cvv);
        results.cvv = this.fillField(cvvField, cardData.cvv);
        
        await this.delay(500);
        
        // Fill name
        const nameField = this.findElement(this.SELECTORS.name);
        if (nameField && nameField.type !== 'email') {
            results.name = this.fillField(nameField, cardData.name);
        }
        
        return results;
    }
    
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Payment status checker
class StatusChecker {
    static ERROR_SELECTORS = [
        '.error', '.alert-error', '.payment-error', '.card-error',
        '[class*="error"]', '[id*="error"]', '.declined', '.failed',
        '[data-testid*="error"]', '.invalid'
    ];
    
    static SUCCESS_SELECTORS = [
        '.success', '.payment-success', '.confirmation', '.complete',
        '[class*="success"]', '[class*="complete"]', '[class*="confirmed"]',
        '[data-testid*="success"]'
    ];
    
    static checkStatus() {
        // Check for errors
        for (let selector of this.ERROR_SELECTORS) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim() && element.offsetParent !== null) {
                return {
                    hasError: true,
                    errorMessage: element.textContent.trim(),
                    isSuccess: false
                };
            }
        }
        
        // Check for success
        for (let selector of this.SUCCESS_SELECTORS) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim() && element.offsetParent !== null) {
                return {
                    hasError: false,
                    isSuccess: true,
                    message: element.textContent.trim()
                };
            }
        }
        
        return {
            hasError: false,
            isSuccess: false,
            message: 'Status unknown'
        };
    }
}

// Main auto checkout controller
class AutoCheckout {
    constructor() {
        this.isRunning = false;
        this.retryCount = 0;
        this.maxRetries = CONFIG.MAX_RETRIES;
    }
    
    async start() {
        if (this.isRunning) {
            console.log('Auto checkout already running');
            return;
        }
        
        this.isRunning = true;
        this.retryCount = 0;
        
        console.log('Starting auto checkout process...');
        
        try {
            await this.processPayment();
        } catch (error) {
            console.error('Auto checkout error:', error);
        } finally {
            this.isRunning = false;
        }
    }
    
    stop() {
        this.isRunning = false;
        console.log('Auto checkout stopped');
    }
    
    async processPayment() {
        while (this.retryCount < this.maxRetries && this.isRunning) {
            try {
                console.log(`Payment attempt ${this.retryCount + 1}/${this.maxRetries}`);
                
                // Generate card data
                const cardData = CardGenerator.generate();
                console.log('Using card data:', cardData);
                
                // Fill form
                const fillResults = await FormFiller.fillForm(cardData);
                console.log('Fill results:', fillResults);
                
                // Wait before submitting
                await FormFiller.delay(CONFIG.SUBMIT_DELAY);
                
                // Submit form
                const submitButton = FormFiller.findElement(FormFiller.SELECTORS.submit);
                if (submitButton && !submitButton.disabled) {
                    submitButton.click();
                    console.log('Form submitted');
                } else {
                    console.log('No submit button found or button disabled');
                    this.retryCount++;
                    continue;
                }
                
                // Wait for processing
                await FormFiller.delay(5000);
                
                // Check status
                const status = StatusChecker.checkStatus();
                console.log('Payment status:', status);
                
                if (status.isSuccess) {
                    console.log('Payment successful!');
                    this.notifySuccess();
                    break;
                } else if (status.hasError) {
                    console.log('Payment failed:', status.errorMessage);
                    this.retryCount++;
                    
                    if (this.retryCount < this.maxRetries) {
                        console.log(`Retrying in ${CONFIG.RETRY_DELAY/1000} seconds...`);
                        await FormFiller.delay(CONFIG.RETRY_DELAY);
                    } else {
                        console.log('Max retries reached');
                        this.notifyFailure();
                    }
                } else {
                    console.log('Payment status unclear, retrying...');
                    this.retryCount++;
                    await FormFiller.delay(CONFIG.RETRY_DELAY);
                }
                
            } catch (error) {
                console.error('Payment processing error:', error);
                this.retryCount++;
                await FormFiller.delay(CONFIG.RETRY_DELAY);
            }
        }
    }
    
    notifySuccess() {
        // Send message to background script
        chrome.runtime.sendMessage({
            action: 'paymentSuccess',
            message: 'Payment completed successfully'
        });
    }
    
    notifyFailure() {
        // Send message to background script
        chrome.runtime.sendMessage({
            action: 'paymentFailure',
            message: 'All payment attempts failed'
        });
    }
}

// Initialize auto checkout
const autoCheckout = new AutoCheckout();

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'startAutoCheckout') {
        autoCheckout.start();
        sendResponse({status: 'started'});
    } else if (request.action === 'stopAutoCheckout') {
        autoCheckout.stop();
        sendResponse({status: 'stopped'});
    } else if (request.action === 'getStatus') {
        sendResponse({
            isRunning: autoCheckout.isRunning,
            retryCount: autoCheckout.retryCount,
            maxRetries: autoCheckout.maxRetries
        });
    }
});

// Auto-start detection
function detectCheckoutPage() {
    const url = window.location.href.toLowerCase();
    const checkoutKeywords = ['checkout', 'payment', 'stripe', 'pay', 'billing'];
    
    return checkoutKeywords.some(keyword => url.includes(keyword));
}

// Auto-start if on checkout page
if (detectCheckoutPage()) {
    console.log('Checkout page detected, starting auto checkout in 3 seconds...');
    setTimeout(() => {
        autoCheckout.start();
    }, 3000);
}

console.log('Propaganda Auto Checkouter content script loaded');
